{"name": "email-system-frontend", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/vue-query": "^5.8.4", "@vueuse/core": "^10.5.0", "@vueuse/motion": "^2.0.0", "autoprefixer": "^10.4.16", "pinia": "^2.1.7", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "vee-validate": "^4.11.8", "vue": "^3.3.8", "vue-router": "^4.2.5"}, "devDependencies": {"@rushstack/eslint-patch": "^1.5.1", "@tsconfig/node18": "^18.2.2", "@types/node": "^20.9.0", "@vitejs/plugin-vue": "^4.5.0", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.4.0", "eslint": "^8.53.0", "eslint-plugin-vue": "^9.18.1", "npm-run-all": "^4.1.5", "prettier": "^3.1.0", "typescript": "~5.2.0", "vite": "^5.0.0", "vue-tsc": "^1.8.22"}}