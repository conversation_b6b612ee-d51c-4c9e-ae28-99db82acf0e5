### 邮箱连接测试API

# 测试环境配置
@baseUrl = http://localhost:8080
@contentType = application/json

# 需要先登录获取token
@authToken = Bearer_TOKEN_HERE

### 1. 测试Gmail连接
POST {{baseUrl}}/api/user/mailboxes/test-connection
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "app_password_here",
  "imapHost": "imap.gmail.com",
  "imapPort": 993,
  "imapSsl": true,
  "smtpHost": "smtp.gmail.com",
  "smtpPort": 587,
  "smtpSsl": true
}

### 2. 测试Outlook连接
POST {{baseUrl}}/api/user/mailboxes/test-connection
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "password123",
  "imapHost": "outlook.office365.com",
  "imapPort": 993,
  "imapSsl": true,
  "smtpHost": "smtp.office365.com",
  "smtpPort": 587,
  "smtpSsl": true
}

### 3. 测试QQ邮箱连接
POST {{baseUrl}}/api/user/mailboxes/test-connection
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "authorization_code",
  "imapHost": "imap.qq.com",
  "imapPort": 993,
  "imapSsl": true,
  "smtpHost": "smtp.qq.com",
  "smtpPort": 587,
  "smtpSsl": true
}

### 4. 测试163邮箱连接
POST {{baseUrl}}/api/user/mailboxes/test-connection
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "password123",
  "imapHost": "imap.163.com",
  "imapPort": 993,
  "imapSsl": true,
  "smtpHost": "smtp.163.com",
  "smtpPort": 587,
  "smtpSsl": true
}

### 5. 测试自建邮箱连接
POST {{baseUrl}}/api/user/mailboxes/test-connection
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "password123",
  "imapHost": "mail.company.com",
  "imapPort": 993,
  "imapSsl": true,
  "smtpHost": "mail.company.com",
  "smtpPort": 587,
  "smtpSsl": true
}

### 6. 测试错误密码连接（应该失败）
POST {{baseUrl}}/api/user/mailboxes/test-connection
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "wrong_password",
  "imapHost": "imap.gmail.com",
  "imapPort": 993,
  "imapSsl": true,
  "smtpHost": "smtp.gmail.com",
  "smtpPort": 587,
  "smtpSsl": true
}

### 7. 测试错误IMAP服务器（应该失败）
POST {{baseUrl}}/api/user/mailboxes/test-connection
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "app_password_here",
  "imapHost": "wrong.imap.server.com",
  "imapPort": 993,
  "imapSsl": true,
  "smtpHost": "smtp.gmail.com",
  "smtpPort": 587,
  "smtpSsl": true
}

### 8. 测试错误端口（应该失败）
POST {{baseUrl}}/api/user/mailboxes/test-connection
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "app_password_here",
  "imapHost": "imap.gmail.com",
  "imapPort": 80,
  "imapSsl": true,
  "smtpHost": "smtp.gmail.com",
  "smtpPort": 80,
  "smtpSsl": true
}

### 9. 测试无SSL连接
POST {{baseUrl}}/api/user/mailboxes/test-connection
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "password123",
  "imapHost": "mail.localserver.com",
  "imapPort": 143,
  "imapSsl": false,
  "smtpHost": "mail.localserver.com",
  "smtpPort": 25,
  "smtpSsl": false
}

### 10. 测试缺少必要参数（应该失败）
POST {{baseUrl}}/api/user/mailboxes/test-connection
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "app_password_here"
}