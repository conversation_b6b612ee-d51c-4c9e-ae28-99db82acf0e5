# 用户设置页面组件

本目录包含了完整的用户设置页面组件，提供了丰富的个人资料管理和系统配置功能。

## 📁 组件结构

```
settings/
├── ProfileSettings.vue          # 个人资料设置
├── SecuritySettings.vue         # 安全设置
├── NotificationSettings.vue     # 通知设置
├── EmailFilterSettings.vue      # 邮件过滤设置
├── EmailSignatureSettings.vue   # 邮件签名设置
├── ApiKeySettings.vue          # API密钥管理
├── ThemeSettings.vue           # 主题设置
└── README.md                   # 说明文档
```

## 🎯 功能特性

### 1. 个人资料设置 (ProfileSettings.vue)
- ✅ **头像管理**: 上传、预览、删除头像
- ✅ **基本信息**: 昵称、语言、时区设置
- ✅ **个人简介**: 支持多行文本编辑
- ✅ **表单验证**: 实时验证和错误提示
- ✅ **响应式设计**: 适配不同屏幕尺寸

### 2. 安全设置 (SecuritySettings.vue)
- ✅ **密码管理**: 修改密码，强度验证
- ✅ **两步验证**: 支持TOTP，二维码设置
- ✅ **会话管理**: 超时设置，注销其他设备
- ✅ **登录历史**: 查看登录记录和安全状态
- ✅ **安全通知**: 异常登录邮件提醒

### 3. 通知设置 (NotificationSettings.vue)
- ✅ **通知方式**: 邮件、桌面、移动端、声音
- ✅ **邮件通知**: 新邮件、重要邮件、安全警报
- ✅ **免打扰模式**: 时间段设置，工作日配置
- ✅ **通知频率**: 邮件摘要，定时发送
- ✅ **测试功能**: 发送测试通知验证设置

### 4. 邮件过滤设置 (EmailFilterSettings.vue)
- ✅ **规则管理**: 创建、编辑、删除过滤规则
- ✅ **条件设置**: 发件人、主题、正文等多种匹配条件
- ✅ **动作配置**: 移动、标签、删除等自动化操作
- ✅ **优先级**: 规则执行顺序控制
- ✅ **启用/禁用**: 灵活控制规则状态

### 5. 邮件签名设置 (EmailSignatureSettings.vue)
- ✅ **双模式编辑**: 纯文本和富文本HTML编辑
- ✅ **富文本工具**: 粗体、斜体、链接、图片等格式
- ✅ **签名模板**: 预设多种专业签名模板
- ✅ **实时预览**: 即时查看签名效果
- ✅ **HTML源码**: 高级用户可直接编辑HTML

### 6. API密钥管理 (ApiKeySettings.vue)
- ✅ **密钥管理**: 创建、编辑、删除、重新生成
- ✅ **权限控制**: 细粒度权限设置
- ✅ **过期管理**: 自定义过期时间
- ✅ **使用统计**: 最后使用时间，状态监控
- ✅ **安全功能**: 密钥隐藏，一键复制

### 7. 主题设置 (ThemeSettings.vue)
- ✅ **预设主题**: 多种精美主题选择
- ✅ **自定义设置**: 主色调、透明度、圆角等
- ✅ **高级配置**: 自定义CSS，完全个性化
- ✅ **导入导出**: 主题配置分享和备份
- ✅ **实时预览**: 设置即时生效

## 🛠️ 技术实现

### 组件架构
- **Vue 3 Composition API**: 现代化的组件开发方式
- **TypeScript**: 完整的类型安全保障
- **响应式设计**: 基于Tailwind CSS的响应式布局
- **毛玻璃效果**: 统一的视觉设计语言

### 状态管理
- **Pinia Store**: 用户认证状态管理
- **本地状态**: 组件内部状态管理
- **API集成**: 与后端服务的数据同步

### 表单处理
- **双向绑定**: v-model数据绑定
- **实时验证**: 输入时即时验证
- **错误处理**: 友好的错误提示
- **自动保存**: 关键设置自动保存

## 🎨 UI组件

### 基础组件
- **Input**: 文本输入框，支持多种类型
- **Textarea**: 多行文本输入
- **Select**: 下拉选择框
- **Switch**: 开关切换器
- **Button**: 多变体按钮
- **Modal**: 模态对话框

### 高级组件
- **GlassCard**: 毛玻璃卡片容器
- **ThemeSelector**: 主题选择器
- **NotificationContainer**: 通知容器

## 📱 响应式支持

### 断点设计
- **移动端** (< 768px): 单栏布局，堆叠显示
- **平板端** (768px - 1024px): 两栏布局
- **桌面端** (> 1024px): 完整三栏布局

### 交互优化
- **触摸友好**: 适合移动设备的交互元素
- **键盘导航**: 完整的键盘访问支持
- **无障碍**: 符合WCAG标准的无障碍设计

## 🔧 使用方法

### 1. 在设置页面中使用
```vue
<template>
  <div v-if="activeTab === 'profile'">
    <ProfileSettings />
  </div>
</template>

<script setup>
import ProfileSettings from '@/components/settings/ProfileSettings.vue'
</script>
```

### 2. 独立页面使用
```vue
<template>
  <div class="container">
    <SecuritySettings />
  </div>
</template>

<script setup>
import SecuritySettings from '@/components/settings/SecuritySettings.vue'
</script>
```

## 🚀 扩展开发

### 添加新设置组件
1. 创建新的Vue组件文件
2. 实现设置界面和逻辑
3. 在主设置页面中注册
4. 添加对应的API接口

### 自定义样式
- 修改Tailwind配置
- 使用CSS变量自定义主题
- 扩展毛玻璃效果样式

## 📋 待办事项

- [ ] 添加设置搜索功能
- [ ] 实现设置导入导出
- [ ] 添加设置历史记录
- [ ] 支持多语言国际化
- [ ] 添加设置向导引导

## 🐛 已知问题

- 部分浏览器的桌面通知权限处理
- 富文本编辑器在某些移动设备上的兼容性
- 主题切换时的动画性能优化

---

这套设置页面组件提供了完整的用户配置管理功能，具有良好的用户体验和扩展性。所有组件都遵循统一的设计规范，确保界面的一致性和专业性。
