### 用户注册API测试

# 测试环境配置
@baseUrl = http://localhost:8080
@contentType = application/json

### 1. 正常用户注册
POST {{baseUrl}}/api/public/user/register
Content-Type: {{contentType}}

{
  "username": "testuser001",
  "email": "<EMAIL>",
  "password": "password123",
  "nickname": "测试用户001"
}

### 2. 用户名为空的注册（应该失败）
POST {{baseUrl}}/api/public/user/register
Content-Type: {{contentType}}

{
  "username": "",
  "email": "<EMAIL>",
  "password": "password123",
  "nickname": "测试用户002"
}

### 3. 邮箱格式错误的注册（应该失败）
POST {{baseUrl}}/api/public/user/register
Content-Type: {{contentType}}

{
  "username": "testuser003",
  "email": "invalid-email-format",
  "password": "password123",
  "nickname": "测试用户003"
}

### 4. 密码为空的注册（应该失败）
POST {{baseUrl}}/api/public/user/register
Content-Type: {{contentType}}

{
  "username": "testuser004",
  "email": "<EMAIL>",
  "password": "",
  "nickname": "测试用户004"
}

### 5. 重复用户名注册（应该失败）
POST {{baseUrl}}/api/public/user/register
Content-Type: {{contentType}}

{
  "username": "testuser001",
  "email": "<EMAIL>",
  "password": "password123",
  "nickname": "测试用户005"
}

### 6. 重复邮箱注册（应该失败）
POST {{baseUrl}}/api/public/user/register
Content-Type: {{contentType}}

{
  "username": "testuser006",
  "email": "<EMAIL>",
  "password": "password123",
  "nickname": "测试用户006"
}

### 7. 最小长度用户名注册
POST {{baseUrl}}/api/public/user/register
Content-Type: {{contentType}}

{
  "username": "abc",
  "email": "<EMAIL>",
  "password": "password123",
  "nickname": "测试用户007"
}

### 8. 长密码注册
POST {{baseUrl}}/api/public/user/register
Content-Type: {{contentType}}

{
  "username": "testuser008",
  "email": "<EMAIL>",
  "password": "verylongpasswordwithspecialcharacters123!@#",
  "nickname": "测试用户008"
}

### 9. 中文昵称注册
POST {{baseUrl}}/api/public/user/register
Content-Type: {{contentType}}

{
  "username": "testuser009",
  "email": "<EMAIL>",
  "password": "password123",
  "nickname": "中文昵称测试用户"
}

### 10. 无昵称注册
POST {{baseUrl}}/api/public/user/register
Content-Type: {{contentType}}

{
  "username": "testuser010",
  "email": "<EMAIL>",
  "password": "password123"
}