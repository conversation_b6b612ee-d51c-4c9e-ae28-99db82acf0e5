### 邮箱管理API测试

# 测试环境配置
@baseUrl = http://localhost:8080
@contentType = application/json

# 需要先登录获取token
@authToken = Bearer_TOKEN_HERE

### 1. 获取邮箱列表
GET {{baseUrl}}/api/user/mailboxes
Authorization: Bearer {{authToken}}

### 2. 分页获取邮箱列表
GET {{baseUrl}}/api/user/mailboxes?page=1&pageSize=5
Authorization: Bearer {{authToken}}

### 3. 按状态筛选邮箱
GET {{baseUrl}}/api/user/mailboxes?status=1
Authorization: Bearer {{authToken}}

### 4. 按类型筛选邮箱
GET {{baseUrl}}/api/user/mailboxes?type=third
Authorization: Bearer {{authToken}}

### 5. 按邮箱地址搜索
GET {{baseUrl}}/api/user/mailboxes?email=gmail
Authorization: Bearer {{authToken}}

### 6. 获取邮箱统计信息
GET {{baseUrl}}/api/user/mailboxes/stats
Authorization: Bearer {{authToken}}

### 7. 获取邮箱提供商配置
GET {{baseUrl}}/api/user/mailboxes/providers
Authorization: Bearer {{authToken}}

### 8. 获取特定邮箱详情
# 注意：需要替换为实际的邮箱ID
GET {{baseUrl}}/api/user/mailboxes/1
Authorization: Bearer {{authToken}}

### 9. 更新邮箱信息
# 注意：需要替换为实际的邮箱ID
PUT {{baseUrl}}/api/user/mailboxes/1
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "autoReceive": false,
  "status": 1
}

### 10. 更新邮箱密码
PUT {{baseUrl}}/api/user/mailboxes/1
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "password": "new_password_here"
}

### 11. 禁用邮箱
PUT {{baseUrl}}/api/user/mailboxes/1
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "status": 0
}

### 12. 启用邮箱
PUT {{baseUrl}}/api/user/mailboxes/1
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "status": 1
}

### 13. 同步邮箱
POST {{baseUrl}}/api/user/mailboxes/1/sync
Authorization: Bearer {{authToken}}

### 14. 删除邮箱
DELETE {{baseUrl}}/api/user/mailboxes/1
Authorization: Bearer {{authToken}}

### 15. 尝试访问已删除的邮箱（应该失败）
GET {{baseUrl}}/api/user/mailboxes/1
Authorization: Bearer {{authToken}}

### 16. 尝试更新不存在的邮箱（应该失败）
PUT {{baseUrl}}/api/user/mailboxes/999
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "status": 1
}

### 17. 尝试删除不存在的邮箱（应该失败）
DELETE {{baseUrl}}/api/user/mailboxes/999
Authorization: Bearer {{authToken}}

### 18. 无token访问邮箱列表（应该失败）
GET {{baseUrl}}/api/user/mailboxes

### 19. 测试邮箱列表的各种筛选组合
GET {{baseUrl}}/api/user/mailboxes?status=1&type=third&page=1&pageSize=10
Authorization: Bearer {{authToken}}

### 20. 测试邮箱同步不存在的邮箱（应该失败）
POST {{baseUrl}}/api/user/mailboxes/999/sync
Authorization: Bearer {{authToken}}