package draft

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"new-email/internal/model"
	"new-email/internal/types"
	"new-email/test/fixtures"

	"github.com/stretchr/testify/assert"
)

// TestDraftManagement 测试草稿管理功能
func TestDraftManagement(t *testing.T) {
	// 初始化测试环境
	svcCtx := fixtures.SetupTestDatabase()
	router := fixtures.SetupTestServer(svcCtx)

	// 创建测试用户
	testUser := &model.User{
		Username: "testuser",
		Email:    "<EMAIL>",
		Password: "$argon2id$v=19$m=65536,t=3,p=2$c29tZXNhbHQ$hash",
		Nickname: "Test User",
		Status:   1,
	}
	err := svcCtx.UserModel.Create(testUser)
	assert.NoError(t, err)

	// 创建测试域名
	testDomain := &model.Domain{
		Name:   "example.com",
		Status: 1,
	}
	err = svcCtx.DomainModel.Create(testDomain)
	assert.NoError(t, err)

	// 创建测试邮箱
	testMailbox := &model.Mailbox{
		UserId:      testUser.Id,
		DomainId:    testDomain.Id,
		Email:       "<EMAIL>",
		Password:    "password123",
		Type:        "imap",
		Status:      1,
		AutoReceive: true,
	}
	err = svcCtx.MailboxModel.Create(testMailbox)
	assert.NoError(t, err)

	// 获取用户token
	token := fixtures.GetUserToken(router, "testuser", "testpassword")

	// 测试创建草稿
	t.Run("创建草稿", func(t *testing.T) {
		createReq := types.DraftCreateReq{
			MailboxId:   testMailbox.Id,
			Subject:     "测试草稿邮件",
			ToEmail:     "<EMAIL>",
			CcEmail:     "<EMAIL>",
			BccEmail:    "",
			Content:     "这是一封测试草稿邮件的内容。",
			ContentType: "text",
		}
		reqBody, _ := json.Marshal(createReq)

		req, _ := http.NewRequest("POST", "/api/user/drafts", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, 200, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, float64(10000), response["code"])

		data := response["data"].(map[string]interface{})
		assert.Equal(t, "测试草稿邮件", data["subject"])
		assert.Equal(t, "<EMAIL>", data["toEmail"])
	})

	// 测试草稿列表
	t.Run("草稿列表", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/user/drafts", nil)
		req.Header.Set("Authorization", "Bearer "+token)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, 200, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, float64(10000), response["code"])

		data := response["data"].(map[string]interface{})
		assert.True(t, data["total"].(float64) >= 1)
		
		list := data["list"].([]interface{})
		assert.True(t, len(list) >= 1)
	})

	// 测试自动保存功能
	t.Run("自动保存草稿", func(t *testing.T) {
		autoSaveReq := types.DraftAutoSaveReq{
			MailboxId:   testMailbox.Id,
			Subject:     "自动保存的草稿",
			ToEmail:     "<EMAIL>",
			Content:     "这是自动保存的草稿内容。",
			ContentType: "text",
		}
		reqBody, _ := json.Marshal(autoSaveReq)

		req, _ := http.NewRequest("POST", "/api/user/drafts/autosave", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, 200, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, float64(10000), response["code"])

		data := response["data"].(map[string]interface{})
		assert.True(t, data["success"].(bool))
		assert.True(t, data["isNew"].(bool))
		assert.True(t, data["draftId"].(float64) > 0)

		// 测试更新现有草稿
		draftId := int64(data["draftId"].(float64))
		autoSaveReq.DraftId = &draftId
		autoSaveReq.Subject = "更新后的草稿标题"
		reqBody, _ = json.Marshal(autoSaveReq)

		req, _ = http.NewRequest("POST", "/api/user/drafts/autosave", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, 200, w.Code)

		err = json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		data = response["data"].(map[string]interface{})
		assert.True(t, data["success"].(bool))
		assert.False(t, data["isNew"].(bool)) // 不是新草稿
		assert.Equal(t, float64(draftId), data["draftId"].(float64))
	})

	// 测试更新草稿
	t.Run("更新草稿", func(t *testing.T) {
		// 先创建一个草稿
		createReq := types.DraftCreateReq{
			MailboxId:   testMailbox.Id,
			Subject:     "待更新的草稿",
			ToEmail:     "<EMAIL>",
			Content:     "原始内容",
			ContentType: "text",
		}
		reqBody, _ := json.Marshal(createReq)

		req, _ := http.NewRequest("POST", "/api/user/drafts", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		var createResponse map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &createResponse)
		draftId := int64(createResponse["data"].(map[string]interface{})["id"].(float64))

		// 更新草稿
		updateReq := types.DraftUpdateReq{
			MailboxId:   testMailbox.Id,
			Subject:     "已更新的草稿",
			ToEmail:     "<EMAIL>",
			Content:     "更新后的内容",
			ContentType: "html",
		}
		reqBody, _ = json.Marshal(updateReq)

		req, _ = http.NewRequest("PUT", fmt.Sprintf("/api/user/drafts/%d", draftId), bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, 200, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		data := response["data"].(map[string]interface{})
		assert.Equal(t, "已更新的草稿", data["subject"])
		assert.Equal(t, "<EMAIL>", data["toEmail"])
		assert.Equal(t, "html", data["contentType"])
	})

	// 测试删除草稿
	t.Run("删除草稿", func(t *testing.T) {
		// 先创建一个草稿
		createReq := types.DraftCreateReq{
			MailboxId:   testMailbox.Id,
			Subject:     "待删除的草稿",
			ToEmail:     "<EMAIL>",
			Content:     "待删除的内容",
			ContentType: "text",
		}
		reqBody, _ := json.Marshal(createReq)

		req, _ := http.NewRequest("POST", "/api/user/drafts", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		var createResponse map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &createResponse)
		draftId := int64(createResponse["data"].(map[string]interface{})["id"].(float64))

		// 删除草稿
		req, _ = http.NewRequest("DELETE", fmt.Sprintf("/api/user/drafts/%d", draftId), nil)
		req.Header.Set("Authorization", "Bearer "+token)

		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, 200, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, float64(10000), response["code"])

		// 验证草稿已被删除
		req, _ = http.NewRequest("GET", fmt.Sprintf("/api/user/drafts/%d", draftId), nil)
		req.Header.Set("Authorization", "Bearer "+token)

		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, 200, w.Code)
		json.Unmarshal(w.Body.Bytes(), &response)
		assert.Equal(t, float64(20000), response["code"]) // 应该返回错误
	})
}

// TestDraftPermission 测试草稿权限
func TestDraftPermission(t *testing.T) {
	// 初始化测试环境
	svcCtx := fixtures.SetupTestDatabase()
	router := fixtures.SetupTestServer(svcCtx)

	// 创建两个测试用户
	user1 := &model.User{
		Username: "user1",
		Email:    "<EMAIL>",
		Password: "$argon2id$v=19$m=65536,t=3,p=2$c29tZXNhbHQ$hash",
		Nickname: "User 1",
		Status:   1,
	}
	err := svcCtx.UserModel.Create(user1)
	assert.NoError(t, err)

	user2 := &model.User{
		Username: "user2",
		Email:    "<EMAIL>",
		Password: "$argon2id$v=19$m=65536,t=3,p=2$c29tZXNhbHQ$hash",
		Nickname: "User 2",
		Status:   1,
	}
	err = svcCtx.UserModel.Create(user2)
	assert.NoError(t, err)

	// 创建测试域名
	testDomain := &model.Domain{
		Name:   "example.com",
		Status: 1,
	}
	err = svcCtx.DomainModel.Create(testDomain)
	assert.NoError(t, err)

	// 为user1创建邮箱
	mailbox1 := &model.Mailbox{
		UserId:      user1.Id,
		DomainId:    testDomain.Id,
		Email:       "<EMAIL>",
		Password:    "password123",
		Type:        "imap",
		Status:      1,
		AutoReceive: true,
	}
	err = svcCtx.MailboxModel.Create(mailbox1)
	assert.NoError(t, err)

	// 获取用户token
	token1 := fixtures.GetUserToken(router, "user1", "testpassword")
	token2 := fixtures.GetUserToken(router, "user2", "testpassword")

	// user1创建草稿
	createReq := types.DraftCreateReq{
		MailboxId:   mailbox1.Id,
		Subject:     "User1的草稿",
		ToEmail:     "<EMAIL>",
		Content:     "这是user1的草稿",
		ContentType: "text",
	}
	reqBody, _ := json.Marshal(createReq)

	req, _ := http.NewRequest("POST", "/api/user/drafts", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token1)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	var createResponse map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &createResponse)
	draftId := int64(createResponse["data"].(map[string]interface{})["id"].(float64))

	// user2尝试访问user1的草稿（应该失败）
	req, _ = http.NewRequest("GET", fmt.Sprintf("/api/user/drafts/%d", draftId), nil)
	req.Header.Set("Authorization", "Bearer "+token2)

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, 200, w.Code)

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// 应该返回权限错误
	assert.Equal(t, float64(20000), response["code"])
	assert.Contains(t, response["msg"].(string), "无权限")
}
