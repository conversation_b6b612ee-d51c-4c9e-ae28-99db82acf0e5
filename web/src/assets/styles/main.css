@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* CSS 变量定义 */
:root {
  /* 深色主题 */
  --color-primary: #6366f1;
  --color-secondary: #8b5cf6;
  --color-background: #0a0a0a;
  --color-surface: #1a1a1a;
  --color-text-primary: #ffffff;
  --color-text-secondary: #a0a0a0;
  --color-text-disabled: #666666;
  --color-glass-light: rgba(255, 255, 255, 0.05);
  --color-glass-medium: rgba(255, 255, 255, 0.08);
  --color-glass-heavy: rgba(255, 255, 255, 0.12);
  --color-glass-border: rgba(255, 255, 255, 0.1);

  /* 功能色 */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;
}

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

html {
  font-family: 'Inter', 'SF Pro Display', system-ui, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  background: var(--color-background);
  color: var(--color-text-primary);
  overflow-x: hidden;
  line-height: 1.6;
}

/* 毛玻璃效果基类 */
@layer components {
  .glass {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: var(--color-glass-light);
    border: 1px solid var(--color-glass-border);
  }

  .glass-medium {
    background: var(--color-glass-medium);
  }

  .glass-heavy {
    background: var(--color-glass-heavy);
  }

  /* 毛玻璃卡片 */
  .glass-card {
    @apply rounded-2xl;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: var(--color-glass-light);
    border: 1px solid var(--color-glass-border);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .glass-card:hover {
    transform: translateY(-2px);
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
  }

  /* 渐变边框效果 */
  .glass-border {
    position: relative;
  }

  .glass-border::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 1px;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.2),
      rgba(255, 255, 255, 0.05)
    );
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box,
          linear-gradient(#fff 0 0);
    mask-composite: exclude;
    -webkit-mask-composite: xor;
  }

  /* 焦点样式 */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-background-primary;
  }

  /* 按钮基础样式 */
  .btn-base {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus-ring;
  }

  .btn-primary {
    @apply btn-base bg-gradient-to-r from-primary-600 to-primary-500 text-white hover:from-primary-700 hover:to-primary-600 shadow-lg hover:shadow-xl;
  }

  .btn-secondary {
    @apply btn-base text-text-primary;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: var(--color-glass-light);
    border: 1px solid var(--color-glass-border);
  }

  .btn-secondary:hover {
    background: var(--color-glass-medium);
  }

  .btn-ghost {
    @apply btn-base text-text-secondary hover:text-text-primary hover:bg-white/5;
  }

  /* 输入框样式 */
  .input-base {
    @apply w-full px-4 py-3 text-text-primary placeholder-text-secondary focus-ring;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    background: var(--color-glass-light);
    border: 1px solid var(--color-glass-border);
  }
}

/* 动画类 */
.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-slide-down {
  animation: slideDown 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0; 
    transform: translateY(20px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes slideDown {
  from { 
    opacity: 0; 
    transform: translateY(-20px); 
  }
  to { 
    opacity: 1; 
    transform: translateY(0); 
  }
}

@keyframes scaleIn {
  from { 
    opacity: 0; 
    transform: scale(0.95); 
  }
  to { 
    opacity: 1; 
    transform: scale(1); 
  }
}

/* 响应式工具类 */
.container-responsive {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.grid-responsive {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

/* 文字选择样式 */
::selection {
  background: rgba(99, 102, 241, 0.3);
  color: white;
}

::-moz-selection {
  background: rgba(99, 102, 241, 0.3);
  color: white;
}



/* 卡片阴影层级 */
.shadow-glass-sm {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.shadow-glass-md {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.shadow-glass-lg {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

.shadow-glass-xl {
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

/* 移动端优化 */
@layer components {
  @media (max-width: 768px) {
    .glass-card {
      backdrop-filter: blur(15px);
      -webkit-backdrop-filter: blur(15px);
    }

    .btn-base {
      @apply px-6 py-3 text-base;
    }

    .input-base {
      @apply py-4 text-base;
    }
  }
}
