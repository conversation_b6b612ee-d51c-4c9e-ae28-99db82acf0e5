### 密码修改API测试

# 测试环境配置
@baseUrl = http://localhost:8080
@contentType = application/json

# 需要先登录获取token
@authToken = Bearer_TOKEN_HERE

### 1. 正常密码修改
POST {{baseUrl}}/api/user/change-password
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "oldPassword": "password123",
  "newPassword": "newpassword123"
}

### 2. 错误的旧密码（应该失败）
POST {{baseUrl}}/api/user/change-password
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "oldPassword": "wrongoldpassword",
  "newPassword": "newpassword123"
}

### 3. 新密码与旧密码相同（应该失败）
POST {{baseUrl}}/api/user/change-password
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "oldPassword": "password123",
  "newPassword": "password123"
}

### 4. 新密码太短（应该失败）
POST {{baseUrl}}/api/user/change-password
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "oldPassword": "password123",
  "newPassword": "123"
}

### 5. 空的旧密码（应该失败）
POST {{baseUrl}}/api/user/change-password
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "oldPassword": "",
  "newPassword": "newpassword123"
}

### 6. 空的新密码（应该失败）
POST {{baseUrl}}/api/user/change-password
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "oldPassword": "password123",
  "newPassword": ""
}

### 7. 测试强密码
POST {{baseUrl}}/api/user/change-password
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "oldPassword": "password123",
  "newPassword": "StrongP@ssw0rd!2024"
}

### 8. 测试包含特殊字符的密码
POST {{baseUrl}}/api/user/change-password
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "oldPassword": "password123",
  "newPassword": "P@ssw0rd#$%^&*()"
}

### 9. 测试很长的密码
POST {{baseUrl}}/api/user/change-password
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "oldPassword": "password123",
  "newPassword": "ThisIsAVeryLongPasswordThatShouldStillBeAcceptedByTheSystem123456789"
}

### 10. 无token访问（应该失败）
POST {{baseUrl}}/api/user/change-password
Content-Type: {{contentType}}

{
  "oldPassword": "password123",
  "newPassword": "newpassword123"
}

### 11. 修改密码后重新登录测试
POST {{baseUrl}}/api/public/user/login
Content-Type: {{contentType}}

{
  "username": "testuser001",
  "password": "newpassword123"
}

### 12. 使用旧密码登录（应该失败）
POST {{baseUrl}}/api/public/user/login
Content-Type: {{contentType}}

{
  "username": "testuser001",
  "password": "password123"
}