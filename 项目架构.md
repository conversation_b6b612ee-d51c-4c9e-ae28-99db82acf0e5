# 邮件管理系统 - 项目架构文档

## 一、项目概述

基于需求文档、技术方案和数据库定义，参考KS项目的编程习惯，设计的邮件管理系统项目架构。

### 技术栈
- **后端**: Go + Gin + GORM + SQLite/MySQL + JWT
- **前端**: Vue3 + Element Plus + Vite + Axios
- **邮件**: go-mail + emersion/go-imap + emersion/go-message
- **日志**: Zap/Logrus

---

## 二、项目目录结构

```
new-email/
├── main.go                     # 主程序入口
├── go.mod                      # Go模块依赖
├── go.sum                      # Go依赖校验
├── etc/                        # 配置文件
│   └── config.yaml            # 应用配置
├── internal/                   # 内部代码
│   ├── config/                # 配置处理
│   │   └── config.go          # 配置结构体和加载
│   ├── handler/               # HTTP处理器
│   │   ├── admin.go           # 管理员相关接口
│   │   ├── user.go            # 用户相关接口
│   │   ├── domain.go          # 域名管理接口
│   │   ├── mailbox.go         # 邮箱管理接口
│   │   ├── email.go           # 邮件相关接口
│   │   ├── template.go        # 邮件模板接口
│   │   ├── signature.go       # 邮件签名接口
│   │   ├── rule.go            # 规则管理接口
│   │   ├── log.go             # 日志查询接口
│   │   ├── api_key.go         # API密钥管理接口
│   │   └── draft.go           # 草稿管理接口

│   ├── model/                 # 数据模型层
│   │   ├── user.go            # 用户模型
│   │   ├── admin.go           # 管理员模型
│   │   ├── domain.go          # 域名模型
│   │   ├── mailbox.go         # 邮箱模型
│   │   ├── email.go           # 邮件模型
│   │   ├── email_attachment.go # 邮件附件模型
│   │   ├── email_template.go  # 邮件模板模型
│   │   ├── email_signature.go # 邮件签名模型
│   │   ├── verification_rule.go # 验证码规则模型
│   │   ├── user_verification_rule.go # 用户验证码规则关联模型
│   │   ├── forward_rule.go    # 转发规则模型
│   │   ├── anti_spam_rule.go  # 反垃圾规则模型
│   │   ├── operation_log.go   # 操作日志模型
│   │   ├── email_log.go       # 邮件日志模型
│   │   ├── api_key.go         # API密钥模型
│   │   ├── verification_code.go # 验证码记录模型
│   │   └── email_draft.go     # 草稿邮件模型
│   ├── result/                # 统一响应结构
│   │   ├── result.go          # 响应结构定义
│   │   └── error.go           # 错误码定义
│   ├── constant/              # 系统常量
│   │   ├── status.go          # 状态常量
│   │   ├── role.go            # 角色常量
│   │   ├── email.go           # 邮件相关常量
│   │   └── rule.go            # 规则相关常量
│   ├── types/                 # 接口类型定义
│   │   ├── admin.go           # 管理员相关请求/响应类型
│   │   ├── user.go            # 用户相关请求/响应类型
│   │   ├── domain.go          # 域名相关请求/响应类型
│   │   ├── mailbox.go         # 邮箱相关请求/响应类型
│   │   ├── email.go           # 邮件相关请求/响应类型
│   │   ├── template.go        # 模板相关请求/响应类型
│   │   ├── signature.go       # 签名相关请求/响应类型
│   │   ├── rule.go            # 规则相关请求/响应类型
│   │   ├── log.go             # 日志相关请求/响应类型
│   │   ├── api_key.go         # API密钥相关请求/响应类型
│   │   ├── draft.go           # 草稿相关请求/响应类型
│   │   └── common.go          # 通用类型定义
│   ├── router/                # 路由配置
│   │   ├── router.go          # 主路由配置
│   │   ├── admin.go           # 管理员路由
│   │   ├── user.go            # 用户路由
│   │   └── api.go             # API路由
│   ├── middleware/            # 中间件
│   │   ├── auth.go            # 身份验证中间件
│   │   ├── admin_auth.go      # 管理员权限中间件
│   │   ├── api_auth.go        # API密钥验证中间件
│   │   ├── cors.go            # 跨域中间件
│   │   ├── log.go             # 日志中间件
│   │   └── rate_limit.go      # 限流中间件
│   ├── mail/                  # 邮件处理模块
│   │   ├── smtp.go            # SMTP发送
│   │   ├── imap.go            # IMAP接收
│   │   ├── parser.go          # 邮件解析
│   │   ├── attachment.go      # 附件处理
│   │   └── scheduler.go       # 定时任务
│   ├── rule/                  # 规则引擎模块
│   │   ├── verification.go    # 验证码提取规则
│   │   ├── forward.go         # 转发规则
│   │   ├── anti_spam.go       # 反垃圾规则
│   │   └── matcher.go         # 规则匹配器
│   └── svc/                   # 服务上下文
│       └── service_context.go # 统一依赖注入
├── pkg/                       # 通用工具包
│   ├── auth/                  # 认证工具
│   │   ├── jwt.go             # JWT工具
│   │   └── password.go        # 密码加密
│   ├── utils/                 # 通用工具
│   │   ├── crypto.go          # 加密解密
│   │   ├── validator.go       # 数据验证
│   │   ├── time.go            # 时间工具
│   │   ├── string.go          # 字符串工具
│   │   └── file.go            # 文件工具
│   ├── logger/                # 日志工具
│   │   └── logger.go          # 日志配置
│   └── database/              # 数据库工具
│       └── migrate.go         # 数据库迁移
├── data/                      # 数据存储
│   ├── email.db               # SQLite数据库文件
│   ├── attachments/           # 邮件附件存储
│   └── logs/                  # 日志文件
├── web/                       # 前端项目
│   ├── index.html             # Vue项目入口
│   ├── package.json           # 前端依赖
│   ├── vite.config.js         # Vite配置
│   ├── src/                   # Vue源码
│   │   ├── main.js            # 入口文件
│   │   ├── App.vue            # 根组件
│   │   ├── config/            # 前端配置
│   │   │   └── index.js       # API配置
│   │   ├── router/            # 路由配置
│   │   │   └── index.js       # 路由定义
│   │   ├── store/             # 状态管理
│   │   │   └── index.js       # Pinia store
│   │   ├── views/             # 页面组件
│   │   │   ├── admin/         # 管理员页面
│   │   │   ├── user/          # 用户页面
│   │   │   └── common/        # 通用页面
│   │   ├── components/        # 通用组件
│   │   │   ├── layout/        # 布局组件
│   │   │   ├── email/         # 邮件相关组件
│   │   │   └── common/        # 通用组件
│   │   ├── utils/             # 前端工具
│   │   │   ├── request.js     # HTTP请求
│   │   │   ├── auth.js        # 认证工具
│   │   │   └── common.js      # 通用工具
│   │   └── assets/            # 静态资源
│   │       ├── css/           # 样式文件
│   │       ├── images/        # 图片资源
│   │       └── icons/         # 图标资源
│   └── dist/                  # 打包输出目录
├── docs/                      # 项目文档
│   ├── api.md                 # API文档
│   ├── deploy.md              # 部署文档
│   └── development.md         # 开发文档
└── scripts/                   # 脚本文件
    ├── build.sh               # 构建脚本
    ├── deploy.sh              # 部署脚本
    └── init.sql               # 初始化SQL
```

---

## 三、核心架构设计

### 3.1 分层架构

遵循KS项目的分层架构模式：

```
┌─────────────────────────────────────────────────────────────┐
│                        前端层 (Vue3)                        │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   管理员界面     │ │    用户界面     │ │    API接口      │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      中间件层 (Middleware)                   │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  身份验证   │ │   权限控制   │ │   日志记录   │ │  限流   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      处理器层 (Handler)                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  用户管理   │ │   邮箱管理   │ │   邮件处理   │ │  规则   │ │
│  │  业务逻辑   │ │   业务逻辑   │ │   业务逻辑   │ │ 业务逻辑│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      数据访问层 (Model)                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  GORM封装   │ │   事务管理   │ │   查询构建   │ │  缓存   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                      数据存储层 (Database)                   │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   SQLite    │ │    MySQL    │ │   文件存储   │ │  日志   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 依赖注入模式

参考KS项目的ServiceContext模式：

```go
type ServiceContext struct {
    Config              config.Config
    DB                  *gorm.DB
    Logger              *zap.Logger

    // Model层实例
    UserModel           *model.UserModel
    AdminModel          *model.AdminModel
    DomainModel         *model.DomainModel
    MailboxModel        *model.MailboxModel
    EmailModel          *model.EmailModel
    EmailAttachmentModel *model.EmailAttachmentModel
    EmailTemplateModel  *model.EmailTemplateModel
    EmailSignatureModel *model.EmailSignatureModel
    VerificationRuleModel *model.VerificationRuleModel
    UserVerificationRuleModel *model.UserVerificationRuleModel
    ForwardRuleModel    *model.ForwardRuleModel
    AntiSpamRuleModel   *model.AntiSpamRuleModel
    OperationLogModel   *model.OperationLogModel
    EmailLogModel       *model.EmailLogModel
    ApiKeyModel         *model.ApiKeyModel
    VerificationCodeModel *model.VerificationCodeModel
    EmailDraftModel     *model.EmailDraftModel

    // 邮件处理模块（后续扩展）
    // SmtpClient          *mail.SmtpClient
    // ImapClient          *mail.ImapClient
    // EmailParser         *mail.EmailParser
    // AttachmentHandler   *mail.AttachmentHandler
    // EmailScheduler      *mail.EmailScheduler

    // 规则引擎模块（后续扩展）
    // VerificationMatcher *rule.VerificationMatcher
    // ForwardMatcher      *rule.ForwardMatcher
    // AntiSpamMatcher     *rule.AntiSpamMatcher
    // RuleMatcher         *rule.RuleMatcher
}
```

### 3.3 编程规范

严格遵循KS项目的编程规范：

#### 3.3.1 数据库操作规范
- 所有数据库操作必须使用GORM提供的方法封装
- 禁止任何层级直接使用原生SQL
- Model层仅定义结构体和封装GORM操作方法

#### 3.3.2 分层职责规范
- **Handler层**: 处理HTTP请求/响应，包含业务逻辑，直接调用Model层
- **Model层**: 数据访问层，封装GORM操作，不包含业务逻辑

#### 3.3.3 响应格式规范
- 所有接口响应使用`internal/result`包统一封装
- 成功响应: `result.SuccessResult(data)`
- 失败响应: `result.ErrorXxx.AddError(err)`
- 列表响应: `result.ListResult(data, page, pageSize, total)`

#### 3.3.4 错误处理规范
- 统一错误码定义在`internal/result/error.go`
- 错误信息支持链式添加: `ErrorAdd.AddError(err)`
- 日志记录使用结构化日志格式

---

## 四、核心模块设计

### 4.1 邮件处理模块 (internal/mail)

```go
// SMTP发送模块
type SmtpClient struct {
    config SmtpConfig
    pool   *sync.Pool
}

// IMAP接收模块
type ImapClient struct {
    config ImapConfig
    conn   *imap.Client
}

// 邮件解析模块
type EmailParser struct {
    attachmentPath string
    maxSize        int64
}

// 附件处理模块
type AttachmentHandler struct {
    storagePath string
    maxSize     int64
}

// 定时任务模块
type EmailScheduler struct {
    cron     *cron.Cron
    svcCtx   *svc.ServiceContext
}
```

### 4.2 规则引擎模块 (internal/rule)

```go
// 验证码提取规则
type VerificationMatcher struct {
    rules []VerificationRule
}

// 转发规则匹配
type ForwardMatcher struct {
    rules []ForwardRule
}

// 反垃圾规则匹配
type AntiSpamMatcher struct {
    rules []AntiSpamRule
}

// 统一规则匹配器
type RuleMatcher struct {
    verificationMatcher *VerificationMatcher
    forwardMatcher      *ForwardMatcher
    antiSpamMatcher     *AntiSpamMatcher
}
```

### 4.3 认证授权模块 (internal/middleware)

```go
// JWT认证中间件
func AuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // JWT token验证逻辑
    }
}

// 管理员权限中间件
func AdminAuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 管理员权限验证逻辑
    }
}

// API密钥验证中间件
func ApiAuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // API密钥验证逻辑
    }
}
```

---

## 五、配置管理

### 5.1 配置文件结构 (etc/config.yaml)

```yaml
# 应用配置
app:
  name: "邮件管理系统"
  version: "1.0.0"
  debug: true

# Web服务配置
web:
  port: 8081
  mode: "debug"  # debug, release
  read_timeout: 60
  write_timeout: 60

# 数据库配置
database:
  type: "sqlite"  # sqlite, mysql
  sqlite:
    path: "./data/email.db"
  mysql:
    host: "localhost"
    port: 3306
    username: "root"
    password: "password"
    database: "email_system"
    charset: "utf8mb4"
    max_idle_conns: 10
    max_open_conns: 100

# JWT配置
jwt:
  secret: "your-jwt-secret-key"
  expire_hours: 24
  refresh_expire_hours: 168

# 邮件配置
email:
  # 默认SMTP配置
  default_smtp:
    host: "smtp.gmail.com"
    port: 587
    username: ""
    password: ""
    use_tls: true

  # 附件配置
  attachment:
    max_size: 10485760  # 10MB
    storage_path: "./data/attachments"
    allowed_types: ["jpg", "jpeg", "png", "gif", "pdf", "doc", "docx", "xls", "xlsx"]

  # 收信配置
  receive:
    batch_size: 100
    sync_interval: 300  # 5分钟
    max_retries: 3

# 日志配置
log:
  level: "info"  # debug, info, warn, error
  format: "json"  # json, text
  output: "file"  # console, file, both
  file_path: "./data/logs/app.log"
  max_size: 100  # MB
  max_backups: 10
  max_age: 30  # days

# Redis配置（可选，用于缓存和队列）
redis:
  enabled: false
  host: "localhost"
  port: 6379
  password: ""
  database: 0
  pool_size: 10

# 限流配置
rate_limit:
  enabled: true
  requests_per_minute: 60
  burst: 10
```

### 5.2 配置结构体 (internal/config/config.go)

```go
type Config struct {
    App      AppConfig      `yaml:"app"`
    Web      WebConfig      `yaml:"web"`
    Database DatabaseConfig `yaml:"database"`
    JWT      JWTConfig      `yaml:"jwt"`
    Email    EmailConfig    `yaml:"email"`
    Log      LogConfig      `yaml:"log"`
    Redis    RedisConfig    `yaml:"redis"`
    RateLimit RateLimitConfig `yaml:"rate_limit"`
}

type AppConfig struct {
    Name    string `yaml:"name"`
    Version string `yaml:"version"`
    Debug   bool   `yaml:"debug"`
}

type WebConfig struct {
    Port         int `yaml:"port"`
    Mode         string `yaml:"mode"`
    ReadTimeout  int `yaml:"read_timeout"`
    WriteTimeout int `yaml:"write_timeout"`
}

type DatabaseConfig struct {
    Type   string       `yaml:"type"`
    SQLite SQLiteConfig `yaml:"sqlite"`
    MySQL  MySQLConfig  `yaml:"mysql"`
}

type EmailConfig struct {
    DefaultSMTP  SMTPConfig       `yaml:"default_smtp"`
    Attachment   AttachmentConfig `yaml:"attachment"`
    Receive      ReceiveConfig    `yaml:"receive"`
}
```

---

## 六、API设计规范

### 6.1 RESTful API规范

遵循RESTful设计原则，统一API路径格式：

```
# 管理员API
POST   /api/admin/login              # 管理员登录
GET    /api/admin/users              # 获取用户列表
POST   /api/admin/users              # 创建用户
PUT    /api/admin/users/:id          # 更新用户
DELETE /api/admin/users/:id          # 删除用户
POST   /api/admin/users/batch        # 批量操作用户

# 用户API
POST   /api/user/login               # 用户登录
POST   /api/user/register            # 用户注册
GET    /api/user/profile             # 获取用户信息
PUT    /api/user/profile             # 更新用户信息

# 邮箱管理API
GET    /api/mailboxes                # 获取邮箱列表
POST   /api/mailboxes                # 添加邮箱
PUT    /api/mailboxes/:id            # 更新邮箱
DELETE /api/mailboxes/:id            # 删除邮箱
POST   /api/mailboxes/batch          # 批量操作邮箱
POST   /api/mailboxes/import         # 批量导入邮箱
POST   /api/mailboxes/:id/sync       # 同步邮箱

# 邮件API
GET    /api/emails                   # 获取邮件列表
GET    /api/emails/:id               # 获取邮件详情
POST   /api/emails/send              # 发送邮件
POST   /api/emails/draft             # 保存草稿
PUT    /api/emails/:id/read          # 标记已读
PUT    /api/emails/:id/star          # 标记星标
DELETE /api/emails/:id               # 删除邮件

# 域名管理API
GET    /api/domains                  # 获取域名列表
POST   /api/domains                  # 添加域名
PUT    /api/domains/:id              # 更新域名
DELETE /api/domains/:id              # 删除域名
POST   /api/domains/:id/verify       # 验证域名

# 规则管理API
GET    /api/rules/verification       # 获取验证码规则
POST   /api/rules/verification       # 创建验证码规则
PUT    /api/rules/verification/:id   # 更新验证码规则
DELETE /api/rules/verification/:id   # 删除验证码规则

GET    /api/rules/forward            # 获取转发规则
POST   /api/rules/forward            # 创建转发规则
PUT    /api/rules/forward/:id        # 更新转发规则
DELETE /api/rules/forward/:id        # 删除转发规则

GET    /api/rules/anti-spam          # 获取反垃圾规则
POST   /api/rules/anti-spam          # 创建反垃圾规则
PUT    /api/rules/anti-spam/:id      # 更新反垃圾规则
DELETE /api/rules/anti-spam/:id      # 删除反垃圾规则

# 验证码API
GET    /api/verification-codes       # 获取验证码列表
GET    /api/verification-codes/:id   # 获取验证码详情
PUT    /api/verification-codes/:id/used # 标记验证码已使用

# 日志API
GET    /api/logs/operation           # 获取操作日志
GET    /api/logs/email               # 获取邮件日志

# API密钥管理
GET    /api/api-keys                 # 获取API密钥列表
POST   /api/api-keys                 # 创建API密钥
PUT    /api/api-keys/:id             # 更新API密钥
DELETE /api/api-keys/:id             # 删除API密钥

# 公共API（通过API密钥访问）
GET    /api/public/emails            # 获取邮件（API密钥访问）
GET    /api/public/verification-codes # 获取验证码（API密钥访问）
```

### 6.2 统一响应格式

参考KS项目的响应格式：

```go
// 成功响应
{
    "code": 0,
    "msg": "success",
    "data": {
        // 具体数据
    }
}

// 列表响应
{
    "code": 0,
    "msg": "success",
    "data": {
        "list": [...],
        "page": 1,
        "pageSize": 20,
        "total": 100
    }
}

// 错误响应
{
    "code": 10001,
    "msg": "绑定参数错误：invalid email format",
    "data": null
}
```

---

## 七、数据库设计

### 7.1 数据库连接管理

参考KS项目的数据库连接方式：

```go
func NewServiceContext(c config.Config) *ServiceContext {
    var db *gorm.DB
    var err error

    switch c.Database.Type {
    case "sqlite":
        db, err = gorm.Open(sqlite.Open(c.Database.SQLite.Path), &gorm.Config{})
    case "mysql":
        dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=True&loc=Local",
            c.Database.MySQL.Username,
            c.Database.MySQL.Password,
            c.Database.MySQL.Host,
            c.Database.MySQL.Port,
            c.Database.MySQL.Database,
            c.Database.MySQL.Charset)
        db, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
    }

    if err != nil {
        log.Fatalln("连接数据库失败", "error", err.Error())
    }

    // 自动迁移
    if err = autoMigrate(db); err != nil {
        log.Fatalln("数据表迁移失败", "error", err.Error())
    }

    return &ServiceContext{
        Config: c,
        DB:     db,
        // 初始化各种Model...
    }
}
```

### 7.2 数据库迁移

```go
func autoMigrate(db *gorm.DB) error {
    return db.AutoMigrate(
        &model.User{},
        &model.Admin{},
        &model.Domain{},
        &model.Mailbox{},
        &model.Email{},
        &model.EmailAttachment{},
        &model.EmailTemplate{},
        &model.EmailSignature{},
        &model.VerificationRule{},
        &model.UserVerificationRule{},
        &model.ForwardRule{},
        &model.AntiSpamRule{},
        &model.OperationLog{},
        &model.EmailLog{},
        &model.ApiKey{},
        &model.VerificationCode{},
        &model.EmailDraft{},
    )
}
```

---

## 八、部署架构

### 8.1 开发环境

```bash
# 启动后端服务
go run main.go -f etc/config.yaml

# 启动前端开发服务
cd web && npm run dev
```

### 8.2 生产环境

```bash
# 构建前端
cd web && npm run build

# 构建后端
go build -o email-system main.go

# 启动服务
./email-system -f etc/config.yaml
```

### 8.3 Docker部署

```dockerfile
# Dockerfile
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go mod tidy && go build -o email-system main.go

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/
COPY --from=builder /app/email-system .
COPY --from=builder /app/etc ./etc
COPY --from=builder /app/web/dist ./web/dist
EXPOSE 8081
CMD ["./email-system"]
```

---

## 九、开发规范总结

### 9.1 Go代码规范

1. **错误处理优先**：
```go
if o, err := newObject(); err != nil {
    log.Fatalln("连接数据库失败", "error", err.Error())
}
```

2. **统一命名规范**：
   - 文件名：snake_case (user.go)
   - 结构体：PascalCase (UserHandler)
   - 方法名：PascalCase (GetUserById)
   - 变量名：camelCase (userId)

3. **依赖注入**：
   - 所有依赖通过ServiceContext注入
   - Handler层通过构造函数接收ServiceContext
   - Handler层通过ServiceContext获取Model实例

4. **事务处理**：
```go
tx := h.svcCtx.DB.Begin()
defer func() {
    if r := recover(); r != nil {
        tx.Rollback()
    }
}()
// 业务逻辑
tx.Commit()
```

### 9.2 前端代码规范

1. **组件命名**：PascalCase (UserManagement.vue)
2. **API调用**：统一使用request.js封装
3. **状态管理**：使用Pinia进行状态管理
4. **路由配置**：支持权限控制和动态路由

### 9.3 API设计规范

1. **RESTful风格**：使用标准HTTP方法
2. **统一响应格式**：使用result包封装
3. **错误处理**：统一错误码和错误信息
4. **参数验证**：使用validator进行参数校验

---

## 十、总结

本项目架构完全基于需求文档、技术方案和数据库定义，并严格遵循KS项目的编程习惯和最佳实践：

### 主要特点
1. **分层清晰**：Handler -> Model 两层架构
2. **依赖注入**：ServiceContext统一管理所有依赖
3. **无外键设计**：应用层维护数据一致性
4. **统一响应**：result包封装所有API响应
5. **配置管理**：YAML配置文件，支持多环境
6. **错误处理**：统一错误码和链式错误信息
7. **日志记录**：结构化日志，支持多种输出方式

### 技术优势
1. **高性能**：Go语言 + Gin框架
2. **易维护**：清晰的分层架构和编程规范
3. **易扩展**：模块化设计，支持功能扩展
4. **易部署**：支持Docker容器化部署
5. **易开发**：完整的开发工具链和规范

该架构为邮件管理系统提供了坚实的技术基础，支持快速开发和长期维护。
