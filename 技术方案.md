# 邮件系统技术方案文档

## 一、技术选型

### 后端技术栈

| 技术               | 用途说明                                     |
|--------------------|----------------------------------------------|
| Go                 | 后端主语言，支持高并发，性能优秀             |
| Gin                | Web 框架，用于构建 RESTful API               |
| go-mail/mail       | 邮件发送库，封装 SMTP 协议                   |
| emersion/go-imap   | 邮件接收库，支持 IMAP 协议                   |
| emersion/go-message| 邮件解析库，用于提取邮件内容、附件等         |
| GORM               | ORM 框架，简化数据库操作                     |
| SQLite             | 开发期使用的轻量级数据库                     |
| MySQL              | 正式环境使用，适合大数据量与高并发场景       |
| JWT                | 身份验证机制                                 |
| Zap / Logrus       | 日志系统                                     |

### 前端技术栈

| 技术          | 用途说明                                     |
|---------------|----------------------------------------------|
| Vue 3         | 前端框架，组合式 API，适合中大型项目          |
| Element Plus  | Vue3 UI 组件库，适合管理后台界面              |
| Pinia         | 状态管理，轻量且官方推荐                     |
| Vue Router    | 路由控制，支持权限动态路由                   |
| Axios         | 网络请求库，用于和后端 API 通信              |
| Vite          | 前端构建工具，支持热更新和快速打包           |

---

## 二、后端目录结构

```
project-root/
├── cmd/                    # 启动入口
│   └── main.go
├── internal/
│   ├── handler/            # 接口处理逻辑（仅调用 service）
│   ├── service/            # 核心业务逻辑（仅调用 model 层）
│   ├── model/              # 数据表结构体 + gorm 方法封装
│   ├── result/             # 统一响应结构封装（如 Success, Error）
│   ├── constant/           # 系统常量（状态码、默认值等）
│   ├── types/              # 接口层请求/响应结构体定义
│   ├── router/             # 路由注册，依赖 svc 初始化 handler
│   ├── middleware/         # 中间件（JWT、CORS、日志等）
│   ├── mail/               # 邮件收发逻辑封装
│   ├── rule/               # 验证码提取与匹配规则逻辑
│   └── svc/                # ServiceContext：统一依赖注入入口
├── pkg/                    # 通用工具类库（加密、格式转换等）
├── go.mod
└── go.sum
```

---

## 三、编程规范

为保持项目可维护性与一致性，制定如下编码规范：

1. **数据库操作**
   - 所有数据库操作必须使用 GORM 提供的方法封装。
   - 禁止任何层级直接使用原生 SQL。

2. **model 层职责**
   - 仅用于定义数据表结构体和封装 GORM 的数据库操作方法。
   - 不允许包含任何业务逻辑。

3. **service 层职责**
   - 只允许调用 model 层提供的方法来操作数据。
   - 严禁在 service 层直接使用原生 SQL 或自行拼接查询。

4. **handler 层职责**
   - 所有接口的响应必须使用 `internal/result` 包提供的封装方法，如 `result.Success()`、`result.Fail()` 等。
   - 不应包含任何数据库操作逻辑。

5. **ServiceContext 使用规范**
   - 所有 model 层实例必须在 `svc.ServiceContext` 中初始化并注册。
   - handler 与 service 层通过 `ServiceContext` 使用 model 实例。

6. **配置信息注入**
   - 系统配置信息统一在 `ServiceContext` 中初始化。
   - handler 与 service 层如需获取配置信息，必须通过 context 中传递获取，不允许直接调用 `config.Load()`。

---

该技术方案适用于中型以上邮件服务项目，具备良好的可扩展性和开发协作规范基础。如需自动生成模板代码结构或初始化 `ServiceContext` 示例，请继续指定。