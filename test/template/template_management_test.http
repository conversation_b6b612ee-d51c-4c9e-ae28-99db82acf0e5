### 邮件模板管理功能测试

### 1. 用户登录获取Token
POST http://localhost:8080/api/public/user/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "testpassword"
}

### 2. 创建邮件模板
POST http://localhost:8080/api/user/templates
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "欢迎邮件模板",
  "category": "通知邮件",
  "subject": "欢迎加入{{company_name}}",
  "content": "<h1>欢迎{{user_name}}</h1><p>感谢您加入{{company_name}}，我们很高兴为您服务。</p><p>您的账户信息：</p><ul><li>用户名：{{username}}</li><li>邮箱：{{email}}</li></ul>",
  "variables": "{\"user_name\":\"用户姓名\",\"company_name\":\"公司名称\",\"username\":\"用户名\",\"email\":\"邮箱地址\"}",
  "description": "用于新用户注册后的欢迎邮件",
  "status": 1
}

### 3. 获取模板列表
GET http://localhost:8080/api/user/templates
Authorization: Bearer {{token}}

### 4. 获取模板列表（带筛选条件）
GET http://localhost:8080/api/user/templates?page=1&pageSize=10&category=通知邮件&name=欢迎
Authorization: Bearer {{token}}

### 5. 获取模板详情
GET http://localhost:8080/api/user/templates/1
Authorization: Bearer {{token}}

### 6. 创建营销邮件模板
POST http://localhost:8080/api/user/templates
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "产品促销模板",
  "category": "营销邮件",
  "subject": "{{product_name}}限时优惠 - 仅限{{days}}天",
  "content": "<div style='font-family: Arial, sans-serif;'><h2 style='color: #e74c3c;'>{{product_name}}限时优惠</h2><p>亲爱的{{customer_name}}，</p><p>我们的<strong>{{product_name}}</strong>正在进行限时优惠活动！</p><div style='background: #f8f9fa; padding: 20px; margin: 20px 0; border-radius: 5px;'><h3>优惠详情：</h3><ul><li>原价：<s>{{original_price}}</s></li><li>现价：<span style='color: #e74c3c; font-size: 1.2em;'>{{sale_price}}</span></li><li>优惠幅度：{{discount}}%</li><li>活动时间：仅限{{days}}天</li></ul></div><p>立即购买，享受超值优惠！</p></div>",
  "variables": "{\"product_name\":\"产品名称\",\"customer_name\":\"客户姓名\",\"original_price\":\"原价\",\"sale_price\":\"现价\",\"discount\":\"优惠幅度\",\"days\":\"活动天数\"}",
  "description": "用于产品促销活动的邮件模板",
  "status": 1
}

### 7. 预览模板
POST http://localhost:8080/api/user/templates/2/preview
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "variables": {
    "product_name": "iPhone 15 Pro",
    "customer_name": "张三",
    "original_price": "¥9999",
    "sale_price": "¥8999",
    "discount": "10",
    "days": "7"
  }
}

### 8. 复制模板
POST http://localhost:8080/api/user/templates/1/copy
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "欢迎邮件模板 - 副本"
}

### 9. 更新模板
PUT http://localhost:8080/api/user/templates/1
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "更新后的欢迎邮件模板",
  "category": "系统邮件",
  "subject": "欢迎加入{{company_name}} - 账户激活成功",
  "content": "<h1>欢迎{{user_name}}</h1><p>恭喜您成功加入{{company_name}}！</p><p>您的账户已激活，现在可以开始使用我们的服务了。</p>",
  "variables": "{\"user_name\":\"用户姓名\",\"company_name\":\"公司名称\"}",
  "description": "用于新用户注册并激活后的欢迎邮件",
  "status": 1
}

### 10. 设置默认模板
PUT http://localhost:8080/api/user/templates/1/default
Authorization: Bearer {{token}}

### 11. 获取模板分类列表
GET http://localhost:8080/api/user/templates/categories
Authorization: Bearer {{token}}

### 12. 创建系统通知模板
POST http://localhost:8080/api/user/templates
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "系统维护通知",
  "category": "系统邮件",
  "subject": "系统维护通知 - {{maintenance_date}}",
  "content": "<div style='border: 1px solid #ffc107; background: #fff3cd; padding: 15px; border-radius: 5px;'><h3 style='color: #856404;'>系统维护通知</h3><p>尊敬的{{user_name}}，</p><p>我们将于<strong>{{maintenance_date}}</strong>进行系统维护，预计维护时间为<strong>{{duration}}</strong>。</p><p>维护期间，系统将暂时无法访问，给您带来的不便敬请谅解。</p><p>维护内容：{{maintenance_content}}</p><p>如有疑问，请联系客服。</p></div>",
  "variables": "{\"user_name\":\"用户姓名\",\"maintenance_date\":\"维护日期\",\"duration\":\"维护时长\",\"maintenance_content\":\"维护内容\"}",
  "description": "用于系统维护通知的邮件模板",
  "status": 1
}

### 13. 删除模板
DELETE http://localhost:8080/api/user/templates/3
Authorization: Bearer {{token}}

### 测试用例说明：
# 1. 首先需要登录获取token
# 2. 创建模板：支持HTML内容和变量定义
# 3. 获取模板列表：支持分页和筛选
# 4. 获取模板详情：查看单个模板的完整信息
# 5. 预览模板：使用实际变量值预览模板效果
# 6. 复制模板：快速创建相似模板
# 7. 更新模板：修改模板内容和配置
# 8. 设置默认模板：设置常用的默认模板
# 9. 获取分类列表：查看所有可用的模板分类
# 10. 删除模板：软删除不需要的模板

### 模板变量语法：
# - 使用 {{variable_name}} 格式定义变量
# - 变量定义使用JSON格式存储
# - 预览时提供实际的变量值进行替换

### 预期响应格式：

### 创建模板成功响应：
# {
#   "code": 10000,
#   "msg": "success",
#   "data": {
#     "id": 1,
#     "userId": 3,
#     "name": "欢迎邮件模板",
#     "category": "通知邮件",
#     "subject": "欢迎加入{{company_name}}",
#     "content": "<h1>欢迎{{user_name}}</h1>...",
#     "variables": "{\"user_name\":\"用户姓名\",\"company_name\":\"公司名称\"}",
#     "description": "用于新用户注册后的欢迎邮件",
#     "status": 1,
#     "createdAt": "2025-07-31T15:00:00Z",
#     "updatedAt": "2025-07-31T15:00:00Z"
#   }
# }

### 预览模板成功响应：
# {
#   "code": 10000,
#   "msg": "success",
#   "data": {
#     "subject": "iPhone 15 Pro限时优惠 - 仅限7天",
#     "content": "<div style='font-family: Arial, sans-serif;'><h2 style='color: #e74c3c;'>iPhone 15 Pro限时优惠</h2><p>亲爱的张三，</p>..."
#   }
# }

### 模板列表响应：
# {
#   "code": 10000,
#   "msg": "success",
#   "data": {
#     "list": [
#       {
#         "id": 1,
#         "name": "欢迎邮件模板",
#         "category": "通知邮件",
#         "subject": "欢迎加入{{company_name}}",
#         "status": 1,
#         "createdAt": "2025-07-31T15:00:00Z"
#       }
#     ],
#     "total": 1,
#     "page": 1,
#     "pageSize": 20
#   }
# }

### 分类列表响应：
# {
#   "code": 10000,
#   "msg": "success",
#   "data": [
#     "通知邮件",
#     "营销邮件",
#     "系统邮件",
#     "通用模板"
#   ]
# }
