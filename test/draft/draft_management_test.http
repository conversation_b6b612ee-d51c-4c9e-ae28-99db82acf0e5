### 草稿管理功能测试

### 1. 用户登录获取Token
POST http://localhost:8080/api/public/user/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "testpassword"
}

### 2. 创建草稿
POST http://localhost:8080/api/user/drafts
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "mailboxId": 2,
  "subject": "测试草稿邮件",
  "toEmail": "<EMAIL>",
  "ccEmail": "<EMAIL>",
  "bccEmail": "",
  "content": "这是一封测试草稿邮件的内容。\n\n请查收。",
  "contentType": "text"
}

### 3. 获取草稿列表
GET http://localhost:8080/api/user/drafts
Authorization: Bearer {{token}}

### 4. 获取草稿列表（带分页和筛选）
GET http://localhost:8080/api/user/drafts?page=1&pageSize=10&subject=测试
Authorization: Bearer {{token}}

### 5. 获取草稿详情
GET http://localhost:8080/api/user/drafts/1
Authorization: Bearer {{token}}

### 6. 自动保存草稿（新建）
POST http://localhost:8080/api/user/drafts/autosave
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "mailboxId": 2,
  "subject": "自动保存的草稿",
  "toEmail": "<EMAIL>",
  "ccEmail": "",
  "bccEmail": "",
  "content": "这是自动保存的草稿内容。",
  "contentType": "text"
}

### 7. 自动保存草稿（更新现有）
POST http://localhost:8080/api/user/drafts/autosave
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "draftId": 2,
  "mailboxId": 2,
  "subject": "更新后的自动保存草稿",
  "toEmail": "<EMAIL>",
  "ccEmail": "<EMAIL>",
  "bccEmail": "",
  "content": "这是更新后的自动保存草稿内容。\n\n已添加更多内容。",
  "contentType": "html"
}

### 8. 更新草稿
PUT http://localhost:8080/api/user/drafts/1
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "mailboxId": 2,
  "subject": "已更新的草稿标题",
  "toEmail": "<EMAIL>",
  "ccEmail": "<EMAIL>",
  "bccEmail": "<EMAIL>",
  "content": "<h1>更新后的HTML内容</h1><p>这是更新后的草稿内容。</p>",
  "contentType": "html"
}

### 9. 发送草稿（需要配置SMTP）
POST http://localhost:8080/api/user/drafts/1/send
Authorization: Bearer {{token}}

### 10. 删除草稿
DELETE http://localhost:8080/api/user/drafts/3
Authorization: Bearer {{token}}

### 测试用例说明：
# 1. 首先需要登录获取token
# 2. 创建草稿：基本的草稿创建功能
# 3. 获取草稿列表：支持分页和筛选
# 4. 获取草稿详情：查看单个草稿的完整信息
# 5. 自动保存：支持新建和更新现有草稿
# 6. 更新草稿：手动更新草稿内容
# 7. 发送草稿：将草稿转换为正式邮件发送
# 8. 删除草稿：软删除草稿

### 自动保存功能特点：
# - 如果不提供draftId，会创建新草稿
# - 如果提供draftId，会更新现有草稿
# - 返回isNew字段表示是否为新创建的草稿
# - 适合前端定时自动保存功能

### 预期响应格式：

### 创建草稿成功响应：
# {
#   "code": 10000,
#   "msg": "success",
#   "data": {
#     "id": 1,
#     "userId": 3,
#     "mailboxId": 2,
#     "subject": "测试草稿邮件",
#     "fromEmail": "",
#     "toEmail": "<EMAIL>",
#     "ccEmail": "<EMAIL>",
#     "bccEmail": "",
#     "content": "这是一封测试草稿邮件的内容。",
#     "contentType": "text",
#     "attachments": "",
#     "createdAt": "2025-07-31T14:30:00Z",
#     "updatedAt": "2025-07-31T14:30:00Z"
#   }
# }

### 自动保存成功响应：
# {
#   "code": 10000,
#   "msg": "success",
#   "data": {
#     "draftId": 2,
#     "success": true,
#     "message": "草稿自动保存成功",
#     "savedAt": "2025-07-31T14:35:00Z",
#     "isNew": true
#   }
# }

### 发送草稿成功响应：
# {
#   "code": 10000,
#   "msg": "success",
#   "data": {
#     "success": true,
#     "message": "邮件发送成功",
#     "emailId": 10,
#     "sentAt": "2025-07-31T14:40:00Z"
#   }
# }

### 草稿列表响应：
# {
#   "code": 10000,
#   "msg": "success",
#   "data": {
#     "list": [
#       {
#         "id": 1,
#         "subject": "测试草稿邮件",
#         "toEmail": "<EMAIL>",
#         "createdAt": "2025-07-31T14:30:00Z"
#       }
#     ],
#     "total": 1,
#     "page": 1,
#     "pageSize": 20
#   }
# }

### 错误响应示例：
# {
#   "code": 20000,
#   "msg": "邮件主题不能为空",
#   "data": null
# }
