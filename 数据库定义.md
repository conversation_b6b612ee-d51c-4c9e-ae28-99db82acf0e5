# 邮件管理系统 - 数据库定义文档

## 一、数据库设计概述

本文档基于需求文档和技术方案，采用GORM结构体定义数据表结构。数据库支持SQLite（开发环境）和MySQL（生产环境）。

**重要说明：**
- 所有表均不使用外键约束，通过应用层维护数据一致性
- 所有ID字段统一使用xxxId命名格式
- 每个字段都包含详细的中文注释

---

## 二、核心数据表结构

### 1. 用户管理相关

#### 1.1 用户表 (users)
```go
type User struct {
    Id          int64           `gorm:"primaryKey;autoIncrement" json:"id"`                    // 用户ID
    Username    string         `gorm:"uniqueIndex;size:50;not null" json:"username"`         // 用户名
    Email       string         `gorm:"uniqueIndex;size:100;not null" json:"email"`           // 邮箱地址
    Password    string         `gorm:"size:255;not null" json:"-"`                           // 密码（加密存储）
    Nickname    string         `gorm:"size:50" json:"nickname"`                              // 昵称
    Avatar      string         `gorm:"size:255" json:"avatar"`                               // 头像URL
    Status      int            `gorm:"default:1" json:"status"`                              // 状态：1启用 0禁用
    LastLoginAt *time.Time     `json:"last_login_at"`                                        // 最后登录时间
    CreatedAt   time.Time      `json:"created_at"`                                           // 创建时间
    UpdatedAt   time.Time      `json:"updated_at"`                                           // 更新时间
    DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`                                       // 软删除时间
}
```

#### 1.2 管理员表 (admins)
```go
type Admin struct {
    Id          int64           `gorm:"primaryKey;autoIncrement" json:"id"`                    // 管理员ID
    Username    string         `gorm:"uniqueIndex;size:50;not null" json:"username"`         // 管理员用户名
    Email       string         `gorm:"uniqueIndex;size:100;not null" json:"email"`           // 管理员邮箱
    Password    string         `gorm:"size:255;not null" json:"-"`                           // 密码（加密存储）
    Nickname    string         `gorm:"size:50" json:"nickname"`                              // 昵称
    Avatar      string         `gorm:"size:255" json:"avatar"`                               // 头像URL
    Role        string         `gorm:"size:20;default:admin" json:"role"`                    // 角色：admin超级管理员 manager普通管理员
    Status      int            `gorm:"default:1" json:"status"`                              // 状态：1启用 0禁用
    LastLoginAt *time.Time     `json:"last_login_at"`                                        // 最后登录时间
    CreatedAt   time.Time      `json:"created_at"`                                           // 创建时间
    UpdatedAt   time.Time      `json:"updated_at"`                                           // 更新时间
    DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`                                       // 软删除时间
}
```

### 2. 域名管理相关

#### 2.1 域名表 (domains)
```go
type Domain struct {
    Id          int64           `gorm:"primaryKey;autoIncrement" json:"id"`                    // 域名ID
    Name        string         `gorm:"uniqueIndex;size:100;not null" json:"name"`            // 域名
    Status      int            `gorm:"default:1" json:"status"`                              // 状态：1启用 0禁用
    DnsVerified bool           `gorm:"default:false" json:"dns_verified"`                    // DNS验证状态
    DkimRecord  string         `gorm:"type:text" json:"dkim_record"`                         // DKIM记录
    SpfRecord   string         `gorm:"type:text" json:"spf_record"`                          // SPF记录
    DmarcRecord string         `gorm:"type:text" json:"dmarc_record"`                        // DMARC记录
    CreatedAt   time.Time      `json:"created_at"`                                           // 创建时间
    UpdatedAt   time.Time      `json:"updated_at"`                                           // 更新时间
    DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`                                       // 软删除时间
}
```

### 3. 邮箱管理相关

#### 3.1 邮箱账户表 (mailboxes)
```go
type Mailbox struct {
    Id           int64           `gorm:"primaryKey;autoIncrement" json:"id"`                   // 邮箱ID
    UserId       int64           `gorm:"not null;index" json:"user_id"`                       // 用户ID
    DomainId     int64           `gorm:"index" json:"domain_id"`                              // 域名ID（自建邮箱关联域名）
    Email        string         `gorm:"uniqueIndex;size:100;not null" json:"email"`          // 邮箱地址
    Password     string         `gorm:"size:255;not null" json:"-"`                          // 邮箱密码（加密存储）
    Type         string         `gorm:"size:20;not null" json:"type"`                        // 邮箱类型：self自建 third第三方
    Provider     string         `gorm:"size:50" json:"provider"`                             // 邮箱提供商：gmail outlook qq imap
    ImapHost     string         `gorm:"size:100" json:"imap_host"`                           // IMAP服务器地址
    ImapPort     int            `gorm:"default:993" json:"imap_port"`                        // IMAP端口
    SmtpHost     string         `gorm:"size:100" json:"smtp_host"`                           // SMTP服务器地址
    SmtpPort     int            `gorm:"default:587" json:"smtp_port"`                        // SMTP端口
    ClientId     string         `gorm:"size:255" json:"client_id"`                           // OAuth客户端ID
    RefreshToken string         `gorm:"size:500" json:"refresh_token"`                       // OAuth刷新令牌
    Status       int            `gorm:"default:1" json:"status"`                             // 状态：1启用 0禁用
    AutoReceive  bool           `gorm:"default:true" json:"auto_receive"`                    // 是否自动收信
    LastSyncAt   *time.Time     `json:"last_sync_at"`                                        // 最后同步时间
    CreatedAt    time.Time      `json:"created_at"`                                          // 创建时间
    UpdatedAt    time.Time      `json:"updated_at"`                                          // 更新时间
    DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`                                      // 软删除时间
}
```

### 4. 邮件相关

#### 4.1 邮件表 (emails)
```go
type Email struct {
    Id          int64           `gorm:"primaryKey;autoIncrement" json:"id"`                    // 邮件ID
    MailboxId   int64           `gorm:"not null;index" json:"mailbox_id"`                     // 邮箱ID
    MessageId   string         `gorm:"size:255;index" json:"message_id"`                     // 邮件消息ID
    Subject     string         `gorm:"size:500" json:"subject"`                              // 邮件主题
    FromEmail   string         `gorm:"size:100;index" json:"from_email"`                     // 发件人邮箱
    FromName    string         `gorm:"size:100" json:"from_name"`                            // 发件人姓名
    ToEmails    string         `gorm:"type:text" json:"to_emails"`                           // 收件人列表（JSON格式）
    CcEmails    string         `gorm:"type:text" json:"cc_emails"`                           // 抄送列表（JSON格式）
    BccEmails   string         `gorm:"type:text" json:"bcc_emails"`                          // 密送列表（JSON格式）
    ReplyTo     string         `gorm:"size:100" json:"reply_to"`                             // 回复地址
    ContentType string         `gorm:"size:20;default:html" json:"content_type"`             // 内容类型：html text
    Content     string         `gorm:"type:longtext" json:"content"`                         // 邮件内容
    IsRead      bool           `gorm:"default:false" json:"is_read"`                         // 是否已读
    IsStarred   bool           `gorm:"default:false" json:"is_starred"`                      // 是否标星
    Direction   string         `gorm:"size:10;not null" json:"direction"`                    // 方向：sent发送 received接收
    SentAt      *time.Time     `json:"sent_at"`                                              // 发送时间
    ReceivedAt  *time.Time     `json:"received_at"`                                          // 接收时间
    CreatedAt   time.Time      `json:"created_at"`                                           // 创建时间
    UpdatedAt   time.Time      `json:"updated_at"`                                           // 更新时间
    DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`                                       // 软删除时间
}
```

#### 4.2 邮件附件表 (email_attachments)
```go
type EmailAttachment struct {
    Id        int64      `gorm:"primaryKey;autoIncrement" json:"id"`                         // 附件ID
    EmailId   int64      `gorm:"not null;index" json:"email_id"`                            // 邮件ID
    Filename  string    `gorm:"size:255;not null" json:"filename"`                         // 文件名
    FilePath  string    `gorm:"size:500;not null" json:"file_path"`                        // 文件路径
    FileSize  int64     `gorm:"not null" json:"file_size"`                                 // 文件大小（字节）
    MimeType  string    `gorm:"size:100" json:"mime_type"`                                 // MIME类型
    CreatedAt time.Time `json:"created_at"`                                                // 创建时间
}
```

### 5. 邮件模板和签名

#### 5.1 邮件模板表 (email_templates)
```go
type EmailTemplate struct {
    Id          int64           `gorm:"primaryKey;autoIncrement" json:"id"`                    // 模板ID
    UserId      int64           `gorm:"not null;index" json:"user_id"`                        // 用户ID
    Name        string         `gorm:"size:100;not null" json:"name"`                        // 模板名称
    Subject     string         `gorm:"size:500" json:"subject"`                              // 邮件主题
    Content     string         `gorm:"type:longtext" json:"content"`                         // 模板内容
    ContentType string         `gorm:"size:20;default:html" json:"content_type"`             // 内容类型：html text
    IsDefault   bool           `gorm:"default:false" json:"is_default"`                      // 是否默认模板
    Status      int            `gorm:"default:1" json:"status"`                              // 状态：1启用 0禁用
    CreatedAt   time.Time      `json:"created_at"`                                           // 创建时间
    UpdatedAt   time.Time      `json:"updated_at"`                                           // 更新时间
    DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`                                       // 软删除时间
}
```

#### 5.2 邮件签名表 (email_signatures)
```go
type EmailSignature struct {
    Id        int64           `gorm:"primaryKey;autoIncrement" json:"id"`                     // 签名ID
    UserId    int64           `gorm:"not null;index" json:"user_id"`                         // 用户ID
    Name      string         `gorm:"size:100;not null" json:"name"`                         // 签名名称
    Content   string         `gorm:"type:text" json:"content"`                              // 签名内容
    IsDefault bool           `gorm:"default:false" json:"is_default"`                       // 是否默认签名
    Status    int            `gorm:"default:1" json:"status"`                               // 状态：1启用 0禁用
    CreatedAt time.Time      `json:"created_at"`                                            // 创建时间
    UpdatedAt time.Time      `json:"updated_at"`                                            // 更新时间
    DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`                                        // 软删除时间
}
```

### 6. 规则管理相关

#### 6.1 验证码规则表 (verification_rules)
```go
type VerificationRule struct {
    Id          int64           `gorm:"primaryKey;autoIncrement" json:"id"`                    // 规则ID
    UserId      int64           `gorm:"index" json:"user_id"`                                 // 创建人ID，0表示公共规则
    Name        string         `gorm:"size:100;not null" json:"name"`                        // 规则名称
    Pattern     string         `gorm:"type:text;not null" json:"pattern"`                    // 正则表达式
    Description string         `gorm:"type:text" json:"description"`                         // 规则描述
    IsGlobal    bool           `gorm:"default:false" json:"is_global"`                       // 是否全局规则
    Status      int            `gorm:"default:1" json:"status"`                              // 状态：1启用 0禁用
    Priority    int            `gorm:"default:0" json:"priority"`                            // 优先级，数字越大优先级越高
    CreatedAt   time.Time      `json:"created_at"`                                           // 创建时间
    UpdatedAt   time.Time      `json:"updated_at"`                                           // 更新时间
    DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`                                       // 软删除时间
}
```

#### 6.2 用户验证码规则关联表 (user_verification_rules)
```go
type UserVerificationRule struct {
    Id        int64      `gorm:"primaryKey;autoIncrement" json:"id"`                         // 关联ID
    UserId    int64      `gorm:"not null;index" json:"user_id"`                             // 用户ID
    RuleId    int64      `gorm:"not null;index" json:"rule_id"`                             // 规则ID
    Status    int       `gorm:"default:1" json:"status"`                                   // 状态：1启用 0禁用
    CreatedAt time.Time `json:"created_at"`                                                // 创建时间
}
```

#### 6.3 转发规则表 (forward_rules)
```go
type ForwardRule struct {
    Id             int64           `gorm:"primaryKey;autoIncrement" json:"id"`                 // 规则ID
    UserId         int64           `gorm:"not null;index" json:"user_id"`                     // 用户ID
    Name           string         `gorm:"size:100;not null" json:"name"`                     // 规则名称
    FromPattern    string         `gorm:"size:255" json:"from_pattern"`                      // 发件人匹配规则
    SubjectPattern string         `gorm:"size:255" json:"subject_pattern"`                   // 主题匹配规则
    ContentPattern string         `gorm:"type:text" json:"content_pattern"`                  // 内容匹配规则
    ForwardTo      string         `gorm:"size:255;not null" json:"forward_to"`               // 转发目标邮箱
    KeepOriginal   bool           `gorm:"default:true" json:"keep_original"`                 // 是否保留原邮件
    Status         int            `gorm:"default:1" json:"status"`                           // 状态：1启用 0禁用
    Priority       int            `gorm:"default:0" json:"priority"`                         // 优先级
    CreatedAt      time.Time      `json:"created_at"`                                        // 创建时间
    UpdatedAt      time.Time      `json:"updated_at"`                                        // 更新时间
    DeletedAt      gorm.DeletedAt `gorm:"index" json:"-"`                                    // 软删除时间
}
```

#### 6.4 反垃圾规则表 (anti_spam_rules)
```go
type AntiSpamRule struct {
    Id          int64           `gorm:"primaryKey;autoIncrement" json:"id"`                    // 规则ID
    Name        string         `gorm:"size:100;not null" json:"name"`                        // 规则名称
    RuleType    string         `gorm:"size:20;not null" json:"rule_type"`                    // 规则类型：blacklist whitelist keyword regex
    Pattern     string         `gorm:"type:text;not null" json:"pattern"`                    // 匹配模式
    Action      string         `gorm:"size:20;not null" json:"action"`                       // 处理动作：block quarantine mark
    Description string         `gorm:"type:text" json:"description"`                         // 规则描述
    IsGlobal    bool           `gorm:"default:false" json:"is_global"`                       // 是否全局规则
    Status      int            `gorm:"default:1" json:"status"`                              // 状态：1启用 0禁用
    Priority    int            `gorm:"default:0" json:"priority"`                            // 优先级
    CreatedAt   time.Time      `json:"created_at"`                                           // 创建时间
    UpdatedAt   time.Time      `json:"updated_at"`                                           // 更新时间
    DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`                                       // 软删除时间
}
```

### 7. 日志相关

#### 7.1 操作日志表 (operation_logs)
```go
type OperationLog struct {
    Id         int64      `gorm:"primaryKey;autoIncrement" json:"id"`                         // 日志ID
    UserId     int64      `gorm:"index" json:"user_id"`                                      // 用户ID，0表示系统操作
    Action     string    `gorm:"size:100;not null" json:"action"`                           // 操作动作
    Resource   string    `gorm:"size:100" json:"resource"`                                  // 操作资源
    ResourceId int64      `json:"resource_id"`                                               // 资源ID
    Method     string    `gorm:"size:10" json:"method"`                                     // 请求方法
    Path       string    `gorm:"size:255" json:"path"`                                      // 请求路径
    Ip         string    `gorm:"size:45" json:"ip"`                                         // 客户端IP
    UserAgent  string    `gorm:"size:500" json:"user_agent"`                                // 用户代理
    Request    string    `gorm:"type:text" json:"request"`                                  // 请求参数
    Response   string    `gorm:"type:text" json:"response"`                                 // 响应内容
    Status     int       `json:"status"`                                                    // 响应状态码
    Duration   int64     `json:"duration"`                                                  // 执行时长（毫秒）
    CreatedAt  time.Time `json:"created_at"`                                                // 创建时间
}
```

#### 7.2 邮件日志表 (email_logs)
```go
type EmailLog struct {
    Id         int64       `gorm:"primaryKey;autoIncrement" json:"id"`                        // 日志ID
    MailboxId  int64       `gorm:"not null;index" json:"mailbox_id"`                         // 邮箱ID
    EmailId    int64       `gorm:"index" json:"email_id"`                                    // 邮件ID，0表示批量操作
    Type       string     `gorm:"size:20;not null" json:"type"`                             // 日志类型：send receive sync
    Status     string     `gorm:"size:20;not null" json:"status"`                           // 状态：success failed pending
    Message    string     `gorm:"type:text" json:"message"`                                 // 日志消息
    ErrorCode  string     `gorm:"size:50" json:"error_code"`                                // 错误代码
    ErrorMsg   string     `gorm:"type:text" json:"error_msg"`                               // 错误消息
    StartedAt  time.Time  `json:"started_at"`                                               // 开始时间
    FinishedAt *time.Time `json:"finished_at"`                                              // 结束时间
    CreatedAt  time.Time  `json:"created_at"`                                               // 创建时间
}
```

### 8. API访问相关

#### 8.1 API密钥表 (api_keys)
```go
type ApiKey struct {
    Id          int64           `gorm:"primaryKey;autoIncrement" json:"id"`                    // 密钥ID
    UserId      int64           `gorm:"not null;index" json:"user_id"`                        // 用户ID
    Name        string         `gorm:"size:100;not null" json:"name"`                        // 密钥名称
    Key         string         `gorm:"uniqueIndex;size:64;not null" json:"key"`              // API密钥
    Secret      string         `gorm:"size:128;not null" json:"-"`                           // 密钥秘钥（加密存储）
    Permissions string         `gorm:"type:text" json:"permissions"`                         // 权限列表（JSON格式）
    Status      int            `gorm:"default:1" json:"status"`                              // 状态：1启用 0禁用
    LastUsedAt  *time.Time     `json:"last_used_at"`                                         // 最后使用时间
    ExpiresAt   *time.Time     `json:"expires_at"`                                           // 过期时间
    CreatedAt   time.Time      `json:"created_at"`                                           // 创建时间
    UpdatedAt   time.Time      `json:"updated_at"`                                           // 更新时间
    DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`                                       // 软删除时间
}
```

### 9. 验证码提取记录

#### 9.1 验证码记录表 (verification_codes)
```go
type VerificationCode struct {
    Id          int64      `gorm:"primaryKey;autoIncrement" json:"id"`                        // 记录ID
    EmailId     int64      `gorm:"not null;index" json:"email_id"`                           // 邮件ID
    RuleId      int64      `gorm:"not null;index" json:"rule_id"`                            // 规则ID
    Code        string    `gorm:"size:50;not null" json:"code"`                             // 验证码
    Source      string    `gorm:"size:100" json:"source"`                                   // 提取来源（发件人）
    ExtractedAt time.Time `json:"extracted_at"`                                             // 提取时间
    IsUsed      bool      `gorm:"default:false" json:"is_used"`                             // 是否已使用
    UsedAt      *time.Time `json:"used_at"`                                                 // 使用时间
    CreatedAt   time.Time `json:"created_at"`                                               // 创建时间
}
```

### 10. 草稿邮件

#### 10.1 草稿邮件表 (email_drafts)
```go
type EmailDraft struct {
    Id          int64           `gorm:"primaryKey;autoIncrement" json:"id"`                    // 草稿ID
    UserId      int64           `gorm:"not null;index" json:"user_id"`                        // 用户ID
    MailboxId   int64           `gorm:"not null;index" json:"mailbox_id"`                     // 邮箱ID
    Subject     string         `gorm:"size:500" json:"subject"`                              // 邮件主题
    ToEmails    string         `gorm:"type:text" json:"to_emails"`                           // 收件人列表（JSON格式）
    CcEmails    string         `gorm:"type:text" json:"cc_emails"`                           // 抄送列表（JSON格式）
    BccEmails   string         `gorm:"type:text" json:"bcc_emails"`                          // 密送列表（JSON格式）
    ContentType string         `gorm:"size:20;default:html" json:"content_type"`             // 内容类型：html text
    Content     string         `gorm:"type:longtext" json:"content"`                         // 邮件内容
    ScheduledAt *time.Time     `json:"scheduled_at"`                                         // 定时发送时间
    Status      string         `gorm:"size:20;default:draft" json:"status"`                  // 状态：draft scheduled sent
    CreatedAt   time.Time      `json:"created_at"`                                           // 创建时间
    UpdatedAt   time.Time      `json:"updated_at"`                                           // 更新时间
    DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`                                       // 软删除时间
}
```

---

## 三、数据库索引设计

### 主要索引
- `users`: username, email, status
- `admins`: username, email, status, role
- `domains`: name, status
- `mailboxes`: user_id, email, status, type, provider
- `emails`: mailbox_id, from_email, direction, sent_at, received_at, is_read
- `email_logs`: mailbox_id, type, status, started_at
- `verification_codes`: email_id, rule_id, extracted_at, is_used
- `verification_rules`: user_id, status, is_global
- `operation_logs`: user_id, action, created_at
- `api_keys`: user_id, key, status
- `email_drafts`: user_id, mailbox_id, status

### 复合索引
- `emails`: (mailbox_id, direction, sent_at)
- `email_logs`: (mailbox_id, type, status)
- `verification_codes`: (email_id, rule_id)
- `user_verification_rules`: (user_id, rule_id)

---

## 四、数据库初始化

### 默认数据

#### 1. 默认管理员账户
```go
// 插入默认管理员
admin := &Admin{
    Username: "admin",
    Email:    "<EMAIL>",
    Password: "encrypted_password", // 需要加密
    Nickname: "系统管理员",
    Role:     "admin",
    Status:   1,
}
```

#### 2. 默认验证码规则
```go
// 默认验证码规则
rules := []VerificationRule{
    {
        UserId:      0, // 公共规则
        Name:        "通用数字验证码",
        Pattern:     `\b\d{4,8}\b`,
        Description: "匹配4-8位数字验证码",
        IsGlobal:    true,
        Status:      1,
        Priority:    1,
    },
    {
        UserId:      0,
        Name:        "通用字母数字验证码",
        Pattern:     `\b[A-Za-z0-9]{4,8}\b`,
        Description: "匹配4-8位字母数字验证码",
        IsGlobal:    true,
        Status:      1,
        Priority:    2,
    },
    {
        UserId:      0,
        Name:        "邮箱验证码",
        Pattern:     `验证码[：:]\s*([A-Za-z0-9]{4,8})`,
        Description: "匹配邮箱中的验证码格式",
        IsGlobal:    true,
        Status:      1,
        Priority:    3,
    },
}
```

---

## 五、数据库迁移说明

使用GORM的AutoMigrate功能进行数据库迁移：

```go
func AutoMigrate(db *gorm.DB) error {
    return db.AutoMigrate(
        &User{},
        &Admin{},
        &Domain{},
        &Mailbox{},
        &Email{},
        &EmailAttachment{},
        &EmailTemplate{},
        &EmailSignature{},
        &VerificationRule{},
        &UserVerificationRule{},
        &ForwardRule{},
        &AntiSpamRule{},
        &OperationLog{},
        &EmailLog{},
        &ApiKey{},
        &VerificationCode{},
        &EmailDraft{},
    )
}
```

---

## 六、注意事项

### 1. 数据安全
- 密码字段使用`json:"-"`标签避免序列化输出
- API密钥的secret字段需要加密存储
- 敏感操作需要记录操作日志
- 邮箱密码和OAuth令牌需要加密存储

### 2. 性能优化
- 邮件内容使用`longtext`类型支持大容量
- 合理使用索引提升查询性能
- 考虑对历史邮件进行分表或归档
- 大量邮件查询时使用分页和缓存

### 3. 扩展性
- 预留足够的字段长度
- 使用JSON格式存储复杂数据结构（如收件人列表、权限配置）
- 支持软删除便于数据恢复
- 规则系统支持优先级和灵活配置

### 4. 兼容性
- 结构体设计兼容SQLite和MySQL
- 时间字段统一使用time.Time类型
- 字符集使用UTF-8支持多语言
- 不使用外键约束，通过应用层维护数据一致性

### 5. 业务逻辑
- 验证码提取支持多种规则匹配
- 转发规则支持复杂的匹配条件
- 反垃圾规则支持多种过滤策略
- 邮件模板和签名支持富文本格式
- 验证码规则支持用户自定义和公共规则

---

## 七、数据表关系图

```
Users (用户表)
├── Mailboxes (邮箱账户) [user_id]
│   ├── Emails (邮件) [mailbox_id]
│   │   ├── EmailAttachments (附件) [email_id]
│   │   └── VerificationCodes (验证码) [email_id]
│   ├── EmailLogs (邮件日志) [mailbox_id]
│   └── EmailDrafts (草稿) [mailbox_id]
├── EmailTemplates (邮件模板) [user_id]
├── EmailSignatures (邮件签名) [user_id]
├── UserVerificationRules (用户验证码规则) [user_id]
├── ForwardRules (转发规则) [user_id]
└── ApiKeys (API密钥) [user_id]

Admins (管理员表) - 独立表

Domains (域名表)
└── Mailboxes (邮箱账户) [domain_id]

VerificationRules (验证码规则)
├── UserVerificationRules (用户规则关联) [rule_id]
└── VerificationCodes (验证码记录) [rule_id]

AntiSpamRules (反垃圾规则) - 独立表
OperationLogs (操作日志) - 独立表
```

---

## 八、总结

本数据库设计完全满足邮件管理系统的需求：

### 主要特点
1. **无外键设计** - 所有表均不使用外键约束，通过应用层维护数据一致性
2. **统一命名规范** - 所有ID字段使用xxxId格式
3. **完整注释** - 每个字段都有详细的中文注释说明
4. **灵活的规则系统** - 验证码规则支持用户自定义和公共规则

### 功能覆盖
- ✅ 用户和管理员分离管理
- ✅ 邮箱管理（自建+第三方邮箱支持）
- ✅ 域名管理（DNS验证、DKIM等）
- ✅ 验证码规则管理（支持用户自定义和公共规则）
- ✅ 转发规则管理
- ✅ 反垃圾规则管理
- ✅ 邮件收发功能
- ✅ 邮件模板和签名
- ✅ API访问支持
- ✅ 完整的日志系统
- ✅ 草稿和定时发送功能

该设计遵循GORM最佳实践，支持数据库迁移，具备良好的扩展性和维护性。
