#!/bin/bash

# 邮件管理系统自动化测试脚本
# 使用curl命令进行API测试

# 配置
BASE_URL="http://localhost:8080"
CONTENT_TYPE="application/json"
TEST_EMAIL="<EMAIL>"
TEST_USERNAME="autotest_user"
TEST_PASSWORD="AutoTest123!"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志文件
LOG_FILE="test_results_$(date +%Y%m%d_%H%M%S).log"

# 函数：打印测试标题
print_test_title() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
    echo "=== $1 ===" >> $LOG_FILE
}

# 函数：打印测试结果
print_result() {
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✓ PASS${NC}: $2"
        echo "✓ PASS: $2" >> $LOG_FILE
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ FAIL${NC}: $2"
        echo "✗ FAIL: $2" >> $LOG_FILE
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

# 函数：发送HTTP请求并检查响应
test_api() {
    local method=$1
    local url=$2
    local data=$3
    local expected_code=$4
    local test_name=$5
    local auth_header=$6
    
    if [ -n "$auth_header" ]; then
        response=$(curl -s -w "\n%{http_code}" -X $method \
            -H "Content-Type: $CONTENT_TYPE" \
            -H "Authorization: Bearer $auth_header" \
            -d "$data" \
            "$BASE_URL$url")
    else
        response=$(curl -s -w "\n%{http_code}" -X $method \
            -H "Content-Type: $CONTENT_TYPE" \
            -d "$data" \
            "$BASE_URL$url")
    fi
    
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    echo "Request: $method $url" >> $LOG_FILE
    echo "Response Code: $http_code" >> $LOG_FILE
    echo "Response Body: $response_body" >> $LOG_FILE
    echo "---" >> $LOG_FILE
    
    if [ "$http_code" = "$expected_code" ]; then
        print_result 0 "$test_name"
        return 0
    else
        print_result 1 "$test_name (Expected: $expected_code, Got: $http_code)"
        return 1
    fi
}

# 开始测试
echo -e "${YELLOW}开始邮件管理系统API测试${NC}"
echo "测试开始时间: $(date)" > $LOG_FILE
echo "基础URL: $BASE_URL" >> $LOG_FILE
echo "" >> $LOG_FILE

# 1. 健康检查
print_test_title "健康检查"
test_api "GET" "/api/health" "" "200" "系统健康检查"

# 2. 用户注册测试
print_test_title "用户注册功能"

# 2.1 发送验证码
test_api "POST" "/api/public/send-code" \
    '{"type":"email","target":"'$TEST_EMAIL'","length":6,"expireMinutes":10}' \
    "200" "发送注册验证码"

# 2.2 用户注册（不使用验证码）
test_api "POST" "/api/public/user/register" \
    '{"username":"'$TEST_USERNAME'","email":"'$TEST_EMAIL'","password":"'$TEST_PASSWORD'","nickname":"自动测试用户"}' \
    "200" "用户注册"

# 2.3 重复用户名注册（应该失败）
test_api "POST" "/api/public/user/register" \
    '{"username":"'$TEST_USERNAME'","email":"<EMAIL>","password":"'$TEST_PASSWORD'","nickname":"重复用户"}' \
    "200" "重复用户名注册（应该失败）"

# 3. 用户登录测试
print_test_title "用户登录功能"

# 3.1 正常登录
login_response=$(curl -s -X POST \
    -H "Content-Type: $CONTENT_TYPE" \
    -d '{"username":"'$TEST_USERNAME'","password":"'$TEST_PASSWORD'"}' \
    "$BASE_URL/api/public/user/login")

echo "Login Response: $login_response" >> $LOG_FILE

# 提取token（这里需要根据实际响应格式调整）
AUTH_TOKEN=$(echo $login_response | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ -n "$AUTH_TOKEN" ]; then
    print_result 0 "用户登录并获取Token"
    echo "Token: $AUTH_TOKEN" >> $LOG_FILE
else
    print_result 1 "用户登录并获取Token"
    AUTH_TOKEN="dummy_token" # 使用假token继续测试
fi

# 3.2 错误密码登录
test_api "POST" "/api/public/user/login" \
    '{"username":"'$TEST_USERNAME'","password":"wrong_password"}' \
    "200" "错误密码登录（应该失败）"

# 4. 用户资料管理测试
print_test_title "用户资料管理"

# 4.1 获取用户资料
test_api "GET" "/api/user/profile" "" "200" "获取用户资料" "$AUTH_TOKEN"

# 4.2 更新用户资料
test_api "PUT" "/api/user/profile" \
    '{"nickname":"更新后的自动测试用户"}' \
    "200" "更新用户资料" "$AUTH_TOKEN"

# 5. 邮箱管理测试
print_test_title "邮箱管理功能"

# 5.1 获取邮箱提供商配置
test_api "GET" "/api/user/mailboxes/providers" "" "200" "获取邮箱提供商配置" "$AUTH_TOKEN"

# 5.2 获取邮箱列表
test_api "GET" "/api/user/mailboxes" "" "200" "获取邮箱列表" "$AUTH_TOKEN"

# 5.3 获取邮箱统计
test_api "GET" "/api/user/mailboxes/stats" "" "200" "获取邮箱统计" "$AUTH_TOKEN"

# 5.4 添加邮箱（测试配置）
test_api "POST" "/api/user/mailboxes" \
    '{"email":"<EMAIL>","password":"test_password","type":"third","provider":"gmail","autoReceive":true}' \
    "200" "添加Gmail邮箱" "$AUTH_TOKEN"

# 6. 邮件管理测试
print_test_title "邮件管理功能"

# 6.1 获取邮件列表
test_api "GET" "/api/user/emails" "" "200" "获取邮件列表" "$AUTH_TOKEN"

# 6.2 获取已发送邮件
test_api "GET" "/api/user/emails?direction=sent" "" "200" "获取已发送邮件" "$AUTH_TOKEN"

# 6.3 获取收件箱邮件
test_api "GET" "/api/user/emails?direction=received" "" "200" "获取收件箱邮件" "$AUTH_TOKEN"

# 7. 权限测试
print_test_title "权限控制测试"

# 7.1 无token访问受保护资源
test_api "GET" "/api/user/profile" "" "401" "无token访问用户资料（应该失败）"

# 7.2 无效token访问
test_api "GET" "/api/user/profile" "" "401" "无效token访问用户资料（应该失败）" "invalid_token"

# 8. 密码修改测试
print_test_title "密码修改功能"

# 8.1 修改密码
test_api "POST" "/api/user/change-password" \
    '{"oldPassword":"'$TEST_PASSWORD'","newPassword":"NewPassword123!"}' \
    "200" "修改密码" "$AUTH_TOKEN"

# 8.2 使用新密码登录
test_api "POST" "/api/public/user/login" \
    '{"username":"'$TEST_USERNAME'","password":"NewPassword123!"}' \
    "200" "使用新密码登录"

# 8.3 使用旧密码登录（应该失败）
test_api "POST" "/api/public/user/login" \
    '{"username":"'$TEST_USERNAME'","password":"'$TEST_PASSWORD'"}' \
    "200" "使用旧密码登录（应该失败）"

# 测试结果统计
print_test_title "测试结果统计"
echo -e "${BLUE}总测试数: $TOTAL_TESTS${NC}"
echo -e "${GREEN}通过测试: $PASSED_TESTS${NC}"
echo -e "${RED}失败测试: $FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}🎉 所有测试通过！${NC}"
    exit_code=0
else
    echo -e "${RED}❌ 有 $FAILED_TESTS 个测试失败${NC}"
    exit_code=1
fi

# 写入统计信息到日志
echo "" >> $LOG_FILE
echo "=== 测试结果统计 ===" >> $LOG_FILE
echo "总测试数: $TOTAL_TESTS" >> $LOG_FILE
echo "通过测试: $PASSED_TESTS" >> $LOG_FILE
echo "失败测试: $FAILED_TESTS" >> $LOG_FILE
echo "测试结束时间: $(date)" >> $LOG_FILE

echo -e "${YELLOW}详细日志已保存到: $LOG_FILE${NC}"

exit $exit_code