### 邮箱添加API测试

# 测试环境配置
@baseUrl = http://localhost:8080
@contentType = application/json

# 需要先登录获取token
@authToken = Bearer_TOKEN_HERE

### 1. 添加Gmail邮箱
POST {{baseUrl}}/api/user/mailboxes
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "app_password_here",
  "type": "third",
  "provider": "gmail",
  "autoReceive": true
}

### 2. 添加Outlook邮箱
POST {{baseUrl}}/api/user/mailboxes
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "password123",
  "type": "third",
  "provider": "outlook",
  "autoReceive": true
}

### 3. 添加QQ邮箱
POST {{baseUrl}}/api/user/mailboxes
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "authorization_code",
  "type": "third",
  "provider": "qq",
  "autoReceive": false
}

### 4. 添加163邮箱
POST {{baseUrl}}/api/user/mailboxes
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "password123",
  "type": "third",
  "provider": "163",
  "autoReceive": true
}

### 5. 添加自建邮箱
POST {{baseUrl}}/api/user/mailboxes
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "password123",
  "type": "self",
  "provider": "custom",
  "imapHost": "mail.company.com",
  "imapPort": 993,
  "imapSsl": true,
  "smtpHost": "mail.company.com",
  "smtpPort": 587,
  "smtpSsl": true,
  "autoReceive": true
}

### 6. 添加重复邮箱（应该失败）
POST {{baseUrl}}/api/user/mailboxes
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "another_password",
  "type": "third",
  "provider": "gmail",
  "autoReceive": true
}

### 7. 添加无效邮箱格式（应该失败）
POST {{baseUrl}}/api/user/mailboxes
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "email": "invalid-email-format",
  "password": "password123",
  "type": "third",
  "provider": "gmail",
  "autoReceive": true
}

### 8. 添加空密码邮箱（应该失败）
POST {{baseUrl}}/api/user/mailboxes
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "",
  "type": "third",
  "provider": "gmail",
  "autoReceive": true
}

### 9. 添加自建邮箱缺少IMAP配置（应该失败）
POST {{baseUrl}}/api/user/mailboxes
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "password123",
  "type": "self",
  "provider": "custom",
  "autoReceive": true
}

### 10. 无token访问（应该失败）
POST {{baseUrl}}/api/user/mailboxes
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "password123",
  "type": "third",
  "provider": "gmail",
  "autoReceive": true
}