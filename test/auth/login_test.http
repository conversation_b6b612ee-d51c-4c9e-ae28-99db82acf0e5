### 用户登录API测试

# 测试环境配置
@baseUrl = http://localhost:8080
@contentType = application/json

### 1. 正常用户名登录
POST {{baseUrl}}/api/public/user/login
Content-Type: {{contentType}}

{
  "username": "testuser001",
  "password": "password123"
}

### 2. 使用邮箱登录
POST {{baseUrl}}/api/public/user/login
Content-Type: {{contentType}}

{
  "username": "<EMAIL>",
  "password": "password123"
}

### 3. 错误密码登录（应该失败）
POST {{baseUrl}}/api/public/user/login
Content-Type: {{contentType}}

{
  "username": "testuser001",
  "password": "wrongpassword"
}

### 4. 不存在的用户名登录（应该失败）
POST {{baseUrl}}/api/public/user/login
Content-Type: {{contentType}}

{
  "username": "nonexistentuser",
  "password": "password123"
}

### 5. 空用户名登录（应该失败）
POST {{baseUrl}}/api/public/user/login
Content-Type: {{contentType}}

{
  "username": "",
  "password": "password123"
}

### 6. 空密码登录（应该失败）
POST {{baseUrl}}/api/public/user/login
Content-Type: {{contentType}}

{
  "username": "testuser001",
  "password": ""
}

### 7. 测试SQL注入防护
POST {{baseUrl}}/api/public/user/login
Content-Type: {{contentType}}

{
  "username": "admin' OR '1'='1",
  "password": "password"
}

### 8. 测试特殊字符用户名
POST {{baseUrl}}/api/public/user/login
Content-Type: {{contentType}}

{
  "username": "test@user#123",
  "password": "password123"
}

### 9. 测试长用户名
POST {{baseUrl}}/api/public/user/login
Content-Type: {{contentType}}

{
  "username": "verylongusernamethatmightcauseissuesifnothandledproperly",
  "password": "password123"
}

### 10. 测试中文用户名（如果支持）
POST {{baseUrl}}/api/public/user/login
Content-Type: {{contentType}}

{
  "username": "中文用户名",
  "password": "password123"
}

### 保存成功登录的token用于后续测试
# 注意：需要手动复制token到下面的变量中
@authToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

### 11. 使用token获取用户信息
GET {{baseUrl}}/api/user/profile
Authorization: Bearer {{authToken}}

### 12. 测试token过期情况
# 使用过期的token
GET {{baseUrl}}/api/user/profile
Authorization: Bearer expired_token_here

### 13. 测试无效token格式
GET {{baseUrl}}/api/user/profile
Authorization: Bearer invalid.token.format