# 🎨 邮件系统设计规范

## 🌟 设计哲学

### 核心理念
> **"毛玻璃美学 × 深度交互 × 无缝体验"**

我们的设计理念基于三个核心支柱：
1. **视觉层次** - 通过毛玻璃效果创造深度和层次感
2. **交互直觉** - 每个交互都应该符合用户的直觉预期
3. **情感连接** - 通过微动画和细节设计建立情感纽带

---

## 🎭 视觉语言

### 毛玻璃效果系统
```
🔮 毛玻璃层级定义:

Level 1 - 主要卡片:
├── backdrop-filter: blur(20px)
├── background: rgba(255,255,255,0.05)
├── border: 1px solid rgba(255,255,255,0.1)
└── box-shadow: 0 8px 32px rgba(0,0,0,0.3)

Level 2 - 次要卡片:
├── backdrop-filter: blur(15px)
├── background: rgba(255,255,255,0.03)
├── border: 1px solid rgba(255,255,255,0.08)
└── box-shadow: 0 4px 16px rgba(0,0,0,0.2)

Level 3 - 悬浮元素:
├── backdrop-filter: blur(25px)
├── background: rgba(255,255,255,0.08)
├── border: 1px solid rgba(255,255,255,0.15)
└── box-shadow: 0 12px 40px rgba(0,0,0,0.4)
```

### 圆角系统
```
📐 圆角规范:
├── 小元素 (按钮、标签): 8px
├── 卡片组件: 16px
├── 大型容器: 24px
└── 特殊元素 (头像): 50%
```

---

## 🌈 色彩系统

### 深色主题调色板
```
🌙 主色调:
├── 背景色
│   ├── Primary: #0a0a0a (纯黑背景)
│   ├── Secondary: #1a1a1a (次级背景)
│   └── Tertiary: #2a2a2a (三级背景)
│
├── 文字色
│   ├── Primary: #ffffff (主要文字)
│   ├── Secondary: #a0a0a0 (次要文字)
│   └── Disabled: #666666 (禁用文字)
│
└── 品牌色
    ├── Primary: #6366f1 (主品牌色 - 靛蓝)
    ├── Secondary: #8b5cf6 (次品牌色 - 紫色)
    └── Accent: #06b6d4 (强调色 - 青色)

🎨 功能色:
├── Success: #10b981 (成功 - 绿色)
├── Warning: #f59e0b (警告 - 橙色)
├── Error: #ef4444 (错误 - 红色)
└── Info: #3b82f6 (信息 - 蓝色)

🔮 毛玻璃色:
├── Glass-Light: rgba(255,255,255,0.05)
├── Glass-Medium: rgba(255,255,255,0.08)
├── Glass-Heavy: rgba(255,255,255,0.12)
└── Glass-Border: rgba(255,255,255,0.1)
```

### 自定义主题色彩
```
🎨 预设主题:

🌊 海洋蓝主题:
├── Primary: #0ea5e9
├── Secondary: #0284c7
└── Background: #0c1426

🌸 樱花粉主题:
├── Primary: #ec4899
├── Secondary: #db2777
└── Background: #1f0a14

🍃 森林绿主题:
├── Primary: #059669
├── Secondary: #047857
└── Background: #0a1f17

🔥 火焰橙主题:
├── Primary: #ea580c
├── Secondary: #dc2626
└── Background: #1f0a05

💜 神秘紫主题:
├── Primary: #7c3aed
├── Secondary: #6d28d9
└── Background: #1a0b2e
```

---

## 📝 字体系统

### 字体层级
```
📚 字体规范:

标题层级:
├── H1: 32px / 700 / -0.02em (页面主标题)
├── H2: 24px / 600 / -0.01em (区块标题)
├── H3: 20px / 600 / 0em (卡片标题)
└── H4: 18px / 500 / 0em (小标题)

正文层级:
├── Body-Large: 16px / 400 / 0em (主要内容)
├── Body-Medium: 14px / 400 / 0em (次要内容)
└── Body-Small: 12px / 400 / 0.01em (辅助信息)

特殊用途:
├── Caption: 11px / 400 / 0.02em (说明文字)
├── Button: 14px / 500 / 0em (按钮文字)
└── Code: 14px / 400 / 0em / monospace (代码文字)
```

### 字体族
```
🔤 字体栈:
├── 中文: "PingFang SC", "Microsoft YaHei", sans-serif
├── 英文: "SF Pro Display", "Helvetica Neue", sans-serif
├── 代码: "SF Mono", "Monaco", "Consolas", monospace
└── 数字: "SF Pro Display", tabular-nums
```

---

## 🎯 交互设计

### 动画时长规范
```
⏱️ 动画时长:
├── 微交互: 150ms (按钮悬浮、输入框聚焦)
├── 页面切换: 300ms (路由跳转、模态框)
├── 列表动画: 200ms (项目进入、删除)
└── 复杂动画: 500ms (图表加载、数据刷新)

🎭 缓动函数:
├── 标准: cubic-bezier(0.4, 0.0, 0.2, 1)
├── 进入: cubic-bezier(0.0, 0.0, 0.2, 1)
├── 退出: cubic-bezier(0.4, 0.0, 1, 1)
└── 弹性: cubic-bezier(0.68, -0.55, 0.265, 1.55)
```

### 状态设计
```
🎨 交互状态:

按钮状态:
├── Default: 默认状态
├── Hover: 悬浮状态 (亮度+10%, 阴影加深)
├── Active: 激活状态 (缩放0.98, 阴影减少)
├── Focus: 聚焦状态 (外发光效果)
├── Loading: 加载状态 (旋转图标)
└── Disabled: 禁用状态 (透明度50%)

卡片状态:
├── Default: 默认状态
├── Hover: 悬浮状态 (上移4px, 阴影加深)
├── Selected: 选中状态 (边框高亮)
└── Loading: 加载状态 (骨架屏)
```

---

## 📐 布局系统

### 网格系统
```
📏 网格规范:

桌面端 (>1200px):
├── 容器最大宽度: 1200px
├── 列数: 12列
├── 间距: 24px
└── 边距: 32px

平板端 (768px-1200px):
├── 容器最大宽度: 100%
├── 列数: 8列
├── 间距: 20px
└── 边距: 24px

手机端 (<768px):
├── 容器最大宽度: 100%
├── 列数: 4列
├── 间距: 16px
└── 边距: 16px
```

### 间距系统
```
📏 间距规范:
├── xs: 4px (极小间距)
├── sm: 8px (小间距)
├── md: 16px (中等间距)
├── lg: 24px (大间距)
├── xl: 32px (超大间距)
└── xxl: 48px (极大间距)
```

---

## 🧩 组件规范

### 按钮组件
```
🔘 按钮类型:

Primary Button (主要按钮):
├── 背景: 品牌色渐变
├── 文字: 白色
├── 高度: 40px
├── 内边距: 16px 24px
└── 圆角: 8px

Secondary Button (次要按钮):
├── 背景: 透明
├── 边框: 1px solid 品牌色
├── 文字: 品牌色
├── 高度: 40px
├── 内边距: 16px 24px
└── 圆角: 8px

Ghost Button (幽灵按钮):
├── 背景: 毛玻璃效果
├── 文字: 主文字色
├── 高度: 36px
├── 内边距: 12px 16px
└── 圆角: 8px
```

### 输入框组件
```
📝 输入框规范:

标准输入框:
├── 高度: 44px
├── 内边距: 12px 16px
├── 边框: 1px solid rgba(255,255,255,0.1)
├── 背景: 毛玻璃效果
├── 圆角: 8px
└── 聚焦: 边框发光 + 标签上浮

搜索框:
├── 高度: 40px
├── 内边距: 10px 40px 10px 16px
├── 圆角: 20px (胶囊形)
├── 图标: 右侧搜索图标
└── 占位符: 半透明文字
```

---

## 📱 响应式设计

### 断点系统
```
📱 响应式断点:
├── xs: 0px - 575px (超小屏幕)
├── sm: 576px - 767px (小屏幕)
├── md: 768px - 991px (中等屏幕)
├── lg: 992px - 1199px (大屏幕)
└── xl: 1200px+ (超大屏幕)
```

### 适配策略
```
🔄 布局适配:

邮箱主界面:
├── 桌面端: 三栏布局 (侧边栏 + 列表 + 预览)
├── 平板端: 两栏布局 (侧边栏 + 列表)
└── 手机端: 单栏布局 + 底部导航

设置页面:
├── 桌面端: 左右分栏
├── 平板端: 上下分栏
└── 手机端: 单栏 + 标签切换

管理后台:
├── 桌面端: 完整仪表板
├── 平板端: 简化版仪表板
└── 手机端: 卡片式布局
```

---

## 🎪 微交互设计

### 反馈机制
```
💫 交互反馈:

成功操作:
├── 视觉: 绿色勾选图标 + 淡入动画
├── 触觉: 轻微震动 (移动端)
└── 声音: 成功提示音 (可选)

错误操作:
├── 视觉: 红色警告图标 + 抖动动画
├── 触觉: 错误震动 (移动端)
└── 声音: 错误提示音 (可选)

加载状态:
├── 视觉: 旋转加载器 + 骨架屏
├── 进度: 进度条显示
└── 文字: 加载状态描述
```

### 手势支持
```
👆 手势交互:

邮件列表:
├── 左滑: 删除邮件
├── 右滑: 标记已读/未读
├── 长按: 多选模式
└── 下拉: 刷新列表

邮件详情:
├── 左滑: 返回列表
├── 右滑: 下一封邮件
├── 双击: 缩放内容
└── 捏合: 调整字体大小
```

这套设计系统确保了整个邮件系统的视觉一致性和用户体验的连贯性！🎨✨
