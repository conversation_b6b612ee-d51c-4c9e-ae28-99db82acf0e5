package email

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"new-email/internal/model"
	"new-email/internal/types"
	"new-email/test/fixtures"

	"github.com/stretchr/testify/assert"
)

// TestEmailExport 测试邮件导出功能
func TestEmailExport(t *testing.T) {
	// 初始化测试环境
	svcCtx := fixtures.SetupTestDatabase()
	router := fixtures.SetupTestServer(svcCtx)

	// 创建测试用户
	testUser := &model.User{
		Username: "testuser",
		Email:    "<EMAIL>",
		Password: "$argon2id$v=19$m=65536,t=3,p=2$c29tZXNhbHQ$hash",
		Nickname: "Test User",
		Status:   1,
	}
	err := svcCtx.UserModel.Create(testUser)
	assert.NoError(t, err)

	// 创建测试域名
	testDomain := &model.Domain{
		Name:   "example.com",
		Status: 1,
	}
	err = svcCtx.DomainModel.Create(testDomain)
	assert.NoError(t, err)

	// 创建测试邮箱
	testMailbox := &model.Mailbox{
		UserId:   testUser.Id,
		DomainId: testDomain.Id,
		Email:    "<EMAIL>",
		Password: "password123",
		Status:   1,
	}
	err = svcCtx.MailboxModel.Create(testMailbox)
	assert.NoError(t, err)

	// 创建测试邮件
	testEmails := []*model.Email{
		{
			MailboxId:   testMailbox.Id,
			Subject:     "测试邮件1",
			FromEmail:   "<EMAIL>",
			ToEmails:    "<EMAIL>",
			Content:     "这是第一封测试邮件的内容",
			ContentType: "text",
			Direction:   "received",
			IsRead:      false,
			IsStarred:   false,
		},
		{
			MailboxId:   testMailbox.Id,
			Subject:     "测试邮件2",
			FromEmail:   "<EMAIL>",
			ToEmails:    "<EMAIL>",
			CcEmails:    "<EMAIL>",
			Content:     "<p>这是第二封测试邮件的HTML内容</p>",
			ContentType: "html",
			Direction:   "sent",
			IsRead:      true,
			IsStarred:   true,
		},
		{
			MailboxId:   testMailbox.Id,
			Subject:     "重要邮件",
			FromEmail:   "<EMAIL>",
			ToEmails:    "<EMAIL>",
			Content:     "这是一封重要邮件",
			ContentType: "text",
			Direction:   "received",
			IsRead:      true,
			IsStarred:   true,
		},
	}

	for _, email := range testEmails {
		err := svcCtx.EmailModel.Create(email)
		assert.NoError(t, err)
	}

	// 获取用户token
	token := fixtures.GetUserToken(router, "testuser", "testpassword")

	// 测试用例
	tests := []struct {
		name           string
		queryParams    string
		expectedStatus int
		expectedCount  int64
		checkResponse  func(t *testing.T, response map[string]interface{})
	}{
		{
			name:           "导出所有邮件为CSV",
			queryParams:    "format=csv&includeContent=true",
			expectedStatus: 200,
			expectedCount:  3,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				data := response["data"].(map[string]interface{})
				assert.Equal(t, int64(3), int64(data["recordCount"].(float64)))
				assert.Contains(t, data["fileName"].(string), ".csv")
				assert.Contains(t, data["downloadUrl"].(string), "/api/user/emails/download/")
			},
		},
		{
			name:           "导出所有邮件为JSON",
			queryParams:    "format=json&includeContent=false",
			expectedStatus: 200,
			expectedCount:  3,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				data := response["data"].(map[string]interface{})
				assert.Equal(t, int64(3), int64(data["recordCount"].(float64)))
				assert.Contains(t, data["fileName"].(string), ".json")
			},
		},
		{
			name:           "导出所有邮件为EML",
			queryParams:    "format=eml",
			expectedStatus: 200,
			expectedCount:  3,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				data := response["data"].(map[string]interface{})
				assert.Equal(t, int64(3), int64(data["recordCount"].(float64)))
				assert.Contains(t, data["fileName"].(string), ".eml")
			},
		},
		{
			name:           "按方向筛选导出（已发送）",
			queryParams:    "format=csv&direction=sent",
			expectedStatus: 200,
			expectedCount:  1,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				data := response["data"].(map[string]interface{})
				assert.Equal(t, int64(1), int64(data["recordCount"].(float64)))
			},
		},
		{
			name:           "按主题筛选导出",
			queryParams:    "format=json&subject=重要",
			expectedStatus: 200,
			expectedCount:  1,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				data := response["data"].(map[string]interface{})
				assert.Equal(t, int64(1), int64(data["recordCount"].(float64)))
			},
		},
		{
			name:           "按发件人筛选导出",
			queryParams:    "format=csv&fromEmail=sender1",
			expectedStatus: 200,
			expectedCount:  1,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				data := response["data"].(map[string]interface{})
				assert.Equal(t, int64(1), int64(data["recordCount"].(float64)))
			},
		},
		{
			name:           "指定邮箱导出",
			queryParams:    fmt.Sprintf("format=csv&mailboxId=%d", testMailbox.Id),
			expectedStatus: 200,
			expectedCount:  3,
			checkResponse: func(t *testing.T, response map[string]interface{}) {
				data := response["data"].(map[string]interface{})
				assert.Equal(t, int64(3), int64(data["recordCount"].(float64)))
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 发送导出请求
			url := "/api/user/emails/export?" + tt.queryParams
			req, _ := http.NewRequest("GET", url, nil)
			req.Header.Set("Authorization", "Bearer "+token)

			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// 验证响应状态
			assert.Equal(t, tt.expectedStatus, w.Code)

			// 解析响应
			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			assert.NoError(t, err)

			// 验证响应内容
			if tt.expectedStatus == 200 {
				assert.Equal(t, float64(10000), response["code"])
				tt.checkResponse(t, response)
			}
		})
	}
}

// TestEmailExportPermission 测试邮件导出权限
func TestEmailExportPermission(t *testing.T) {
	// 初始化测试环境
	svcCtx := fixtures.SetupTestDatabase()
	router := fixtures.SetupTestServer(svcCtx)

	// 创建两个测试用户
	user1 := &model.User{
		Username: "user1",
		Email:    "<EMAIL>",
		Password: "$argon2id$v=19$m=65536,t=3,p=2$c29tZXNhbHQ$hash",
		Nickname: "User 1",
		Status:   1,
	}
	err := svcCtx.UserModel.Create(user1)
	assert.NoError(t, err)

	user2 := &model.User{
		Username: "user2",
		Email:    "<EMAIL>",
		Password: "$argon2id$v=19$m=65536,t=3,p=2$c29tZXNhbHQ$hash",
		Nickname: "User 2",
		Status:   1,
	}
	err = svcCtx.UserModel.Create(user2)
	assert.NoError(t, err)

	// 创建测试域名
	testDomain := &model.Domain{
		Name:   "example.com",
		Status: 1,
	}
	err = svcCtx.DomainModel.Create(testDomain)
	assert.NoError(t, err)

	// 为user1创建邮箱
	mailbox1 := &model.Mailbox{
		UserId:   user1.Id,
		DomainId: testDomain.Id,
		Email:    "<EMAIL>",
		Password: "password123",
		Status:   1,
	}
	err = svcCtx.MailboxModel.Create(mailbox1)
	assert.NoError(t, err)

	// 为user2创建邮箱
	mailbox2 := &model.Mailbox{
		UserId:   user2.Id,
		DomainId: testDomain.Id,
		Email:    "<EMAIL>",
		Password: "password123",
		Status:   1,
	}
	err = svcCtx.MailboxModel.Create(mailbox2)
	assert.NoError(t, err)

	// 为每个用户创建邮件
	email1 := &model.Email{
		MailboxId: mailbox1.Id,
		Subject:   "User1的邮件",
		FromEmail: "<EMAIL>",
		ToEmails:  "<EMAIL>",
		Content:   "这是user1的邮件",
		Direction: "received",
	}
	err = svcCtx.EmailModel.Create(email1)
	assert.NoError(t, err)

	email2 := &model.Email{
		MailboxId: mailbox2.Id,
		Subject:   "User2的邮件",
		FromEmail: "<EMAIL>",
		ToEmails:  "<EMAIL>",
		Content:   "这是user2的邮件",
		Direction: "received",
	}
	err = svcCtx.EmailModel.Create(email2)
	assert.NoError(t, err)

	// 获取用户token
	token1 := fixtures.GetUserToken(router, "user1", "testpassword")
	token2 := fixtures.GetUserToken(router, "user2", "testpassword")

	// 测试user1只能导出自己的邮件
	req, _ := http.NewRequest("GET", "/api/user/emails/export?format=csv", nil)
	req.Header.Set("Authorization", "Bearer "+token1)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, 200, w.Code)

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].(map[string]interface{})
	assert.Equal(t, int64(1), int64(data["recordCount"].(float64))) // 只能看到1封邮件

	// 测试user1尝试导出user2的邮箱（应该失败）
	url := fmt.Sprintf("/api/user/emails/export?format=csv&mailboxId=%d", mailbox2.Id)
	req, _ = http.NewRequest("GET", url, nil)
	req.Header.Set("Authorization", "Bearer "+token1)

	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, 200, w.Code)

	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// 应该返回错误，因为user1无权访问user2的邮箱
	assert.Equal(t, float64(20000), response["code"])
	assert.Contains(t, response["msg"].(string), "无权限")
}
