package template

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"new-email/internal/model"
	"new-email/internal/types"
	"new-email/test/fixtures"

	"github.com/stretchr/testify/assert"
)

// TestTemplateManagement 测试模板管理功能
func TestTemplateManagement(t *testing.T) {
	// 初始化测试环境
	svcCtx := fixtures.SetupTestDatabase()
	router := fixtures.SetupTestServer(svcCtx)

	// 创建测试用户
	testUser := &model.User{
		Username: "testuser",
		Email:    "<EMAIL>",
		Password: "$argon2id$v=19$m=65536,t=3,p=2$c29tZXNhbHQ$hash",
		Nickname: "Test User",
		Status:   1,
	}
	err := svcCtx.UserModel.Create(testUser)
	assert.NoError(t, err)

	// 获取用户token
	token := fixtures.GetUserToken(router, "testuser", "testpassword")

	// 测试创建模板
	t.Run("创建模板", func(t *testing.T) {
		createReq := types.TemplateCreateReq{
			Name:        "欢迎邮件模板",
			Category:    "通知邮件",
			Subject:     "欢迎加入{{company_name}}",
			Content:     "<h1>欢迎{{user_name}}</h1><p>感谢您加入{{company_name}}，我们很高兴为您服务。</p>",
			Variables:   `{"user_name":"用户姓名","company_name":"公司名称"}`,
			Description: "用于新用户注册后的欢迎邮件",
			Status:      1,
		}
		reqBody, _ := json.Marshal(createReq)

		req, _ := http.NewRequest("POST", "/api/user/templates", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, 200, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, float64(10000), response["code"])

		data := response["data"].(map[string]interface{})
		assert.Equal(t, "欢迎邮件模板", data["name"])
		assert.Equal(t, "通知邮件", data["category"])
		assert.Contains(t, data["content"].(string), "{{user_name}}")
	})

	// 测试模板列表
	t.Run("模板列表", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/user/templates", nil)
		req.Header.Set("Authorization", "Bearer "+token)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, 200, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, float64(10000), response["code"])

		data := response["data"].(map[string]interface{})
		assert.True(t, data["total"].(float64) >= 1)
		
		list := data["list"].([]interface{})
		assert.True(t, len(list) >= 1)
	})

	// 测试模板预览
	t.Run("模板预览", func(t *testing.T) {
		// 先创建一个模板
		createReq := types.TemplateCreateReq{
			Name:        "测试预览模板",
			Category:    "营销邮件",
			Subject:     "{{product_name}}促销活动",
			Content:     "<h2>{{product_name}}限时优惠</h2><p>亲爱的{{customer_name}}，我们的{{product_name}}正在进行限时优惠活动！</p>",
			Variables:   `{"product_name":"产品名称","customer_name":"客户姓名"}`,
			Description: "用于产品促销的邮件模板",
			Status:      1,
		}
		reqBody, _ := json.Marshal(createReq)

		req, _ := http.NewRequest("POST", "/api/user/templates", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		var createResponse map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &createResponse)
		templateId := int64(createResponse["data"].(map[string]interface{})["id"].(float64))

		// 测试预览
		previewReq := types.TemplatePreviewReq{
			Variables: map[string]interface{}{
				"product_name":  "iPhone 15",
				"customer_name": "张三",
			},
		}
		reqBody, _ = json.Marshal(previewReq)

		req, _ = http.NewRequest("POST", fmt.Sprintf("/api/user/templates/%d/preview", templateId), bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, 200, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		data := response["data"].(map[string]interface{})
		assert.Equal(t, "iPhone 15促销活动", data["subject"])
		assert.Contains(t, data["content"].(string), "iPhone 15限时优惠")
		assert.Contains(t, data["content"].(string), "张三")
	})

	// 测试复制模板
	t.Run("复制模板", func(t *testing.T) {
		// 先创建一个模板
		createReq := types.TemplateCreateReq{
			Name:        "原始模板",
			Category:    "系统邮件",
			Subject:     "系统通知",
			Content:     "<p>这是一个系统通知模板</p>",
			Variables:   `{}`,
			Description: "系统通知模板",
			Status:      1,
		}
		reqBody, _ := json.Marshal(createReq)

		req, _ := http.NewRequest("POST", "/api/user/templates", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		var createResponse map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &createResponse)
		templateId := int64(createResponse["data"].(map[string]interface{})["id"].(float64))

		// 复制模板
		copyReq := types.TemplateCopyReq{
			Name: "复制的模板",
		}
		reqBody, _ = json.Marshal(copyReq)

		req, _ = http.NewRequest("POST", fmt.Sprintf("/api/user/templates/%d/copy", templateId), bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, 200, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		data := response["data"].(map[string]interface{})
		assert.Equal(t, "复制的模板", data["name"])
		assert.Equal(t, "系统邮件", data["category"])
		assert.Equal(t, "系统通知", data["subject"])
	})

	// 测试设置默认模板
	t.Run("设置默认模板", func(t *testing.T) {
		// 先创建一个模板
		createReq := types.TemplateCreateReq{
			Name:        "默认模板",
			Category:    "通用模板",
			Subject:     "默认邮件主题",
			Content:     "<p>这是默认邮件模板</p>",
			Variables:   `{}`,
			Description: "默认邮件模板",
			Status:      1,
		}
		reqBody, _ := json.Marshal(createReq)

		req, _ := http.NewRequest("POST", "/api/user/templates", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		var createResponse map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &createResponse)
		templateId := int64(createResponse["data"].(map[string]interface{})["id"].(float64))

		// 设置为默认模板
		req, _ = http.NewRequest("PUT", fmt.Sprintf("/api/user/templates/%d/default", templateId), nil)
		req.Header.Set("Authorization", "Bearer "+token)

		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, 200, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, float64(10000), response["code"])
	})

	// 测试获取分类列表
	t.Run("获取分类列表", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/user/templates/categories", nil)
		req.Header.Set("Authorization", "Bearer "+token)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, 200, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, float64(10000), response["code"])

		categories := response["data"].([]interface{})
		assert.True(t, len(categories) > 0)
	})

	// 测试更新模板
	t.Run("更新模板", func(t *testing.T) {
		// 先创建一个模板
		createReq := types.TemplateCreateReq{
			Name:        "待更新模板",
			Category:    "通用模板",
			Subject:     "原始主题",
			Content:     "<p>原始内容</p>",
			Variables:   `{}`,
			Description: "原始描述",
			Status:      1,
		}
		reqBody, _ := json.Marshal(createReq)

		req, _ := http.NewRequest("POST", "/api/user/templates", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		var createResponse map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &createResponse)
		templateId := int64(createResponse["data"].(map[string]interface{})["id"].(float64))

		// 更新模板
		updateReq := types.TemplateUpdateReq{
			Name:        "已更新模板",
			Category:    "营销邮件",
			Subject:     "更新后主题",
			Content:     "<p>更新后内容</p>",
			Variables:   `{"name":"姓名"}`,
			Description: "更新后描述",
			Status:      1,
		}
		reqBody, _ = json.Marshal(updateReq)

		req, _ = http.NewRequest("PUT", fmt.Sprintf("/api/user/templates/%d", templateId), bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, 200, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		data := response["data"].(map[string]interface{})
		assert.Equal(t, "已更新模板", data["name"])
		assert.Equal(t, "营销邮件", data["category"])
		assert.Equal(t, "更新后主题", data["subject"])
	})

	// 测试删除模板
	t.Run("删除模板", func(t *testing.T) {
		// 先创建一个模板
		createReq := types.TemplateCreateReq{
			Name:        "待删除模板",
			Category:    "通用模板",
			Subject:     "待删除主题",
			Content:     "<p>待删除内容</p>",
			Variables:   `{}`,
			Description: "待删除描述",
			Status:      1,
		}
		reqBody, _ := json.Marshal(createReq)

		req, _ := http.NewRequest("POST", "/api/user/templates", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		var createResponse map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &createResponse)
		templateId := int64(createResponse["data"].(map[string]interface{})["id"].(float64))

		// 删除模板
		req, _ = http.NewRequest("DELETE", fmt.Sprintf("/api/user/templates/%d", templateId), nil)
		req.Header.Set("Authorization", "Bearer "+token)

		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, 200, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, float64(10000), response["code"])

		// 验证模板已被删除
		req, _ = http.NewRequest("GET", fmt.Sprintf("/api/user/templates/%d", templateId), nil)
		req.Header.Set("Authorization", "Bearer "+token)

		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, 200, w.Code)
		json.Unmarshal(w.Body.Bytes(), &response)
		assert.Equal(t, float64(20000), response["code"]) // 应该返回错误
	})
}
