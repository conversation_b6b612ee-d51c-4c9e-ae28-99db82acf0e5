package auth

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"new-email/internal/model"
	"new-email/internal/types"
	"new-email/test/fixtures"

	"github.com/stretchr/testify/assert"
)

// TestLoginAttemptLimit 测试登录失败次数限制
func TestLoginAttemptLimit(t *testing.T) {
	// 初始化测试环境
	svcCtx := fixtures.SetupTestDatabase()
	router := fixtures.SetupTestServer(svcCtx)

	// 创建测试用户
	testUser := &model.User{
		Username: "testuser",
		Email:    "<EMAIL>",
		Password: "$argon2id$v=19$m=65536,t=3,p=2$c29tZXNhbHQ$hash", // 模拟加密密码
		Nickname: "Test User",
		Status:   1,
	}
	err := svcCtx.UserModel.Create(testUser)
	assert.NoError(t, err)

	// 测试用例
	tests := []struct {
		name           string
		username       string
		password       string
		expectedStatus int
		expectedMsg    string
		attemptCount   int
	}{
		{
			name:           "正常登录失败",
			username:       "testuser",
			password:       "wrongpassword",
			expectedStatus: 200,
			expectedMsg:    "密码错误",
			attemptCount:   1,
		},
		{
			name:           "多次登录失败",
			username:       "testuser",
			password:       "wrongpassword",
			expectedStatus: 200,
			expectedMsg:    "密码错误",
			attemptCount:   4, // 总共5次失败
		},
		{
			name:           "超过限制后被锁定",
			username:       "testuser",
			password:       "wrongpassword",
			expectedStatus: 200,
			expectedMsg:    "登录失败次数过多，账户已被锁定，请稍后再试",
			attemptCount:   1, // 第6次尝试
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			for i := 0; i < tt.attemptCount; i++ {
				// 准备请求数据
				loginReq := types.UserLoginReq{
					Username: tt.username,
					Password: tt.password,
				}
				reqBody, _ := json.Marshal(loginReq)

				// 发送登录请求
				req, _ := http.NewRequest("POST", "/api/public/user/login", bytes.NewBuffer(reqBody))
				req.Header.Set("Content-Type", "application/json")
				req.Header.Set("X-Real-IP", "*************") // 模拟固定IP

				w := httptest.NewRecorder()
				router.ServeHTTP(w, req)

				// 验证响应
				assert.Equal(t, tt.expectedStatus, w.Code)

				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)

				// 在最后一次尝试时检查消息
				if i == tt.attemptCount-1 {
					if message, ok := response["message"].(string); ok {
						assert.Contains(t, message, tt.expectedMsg)
					}
				}
			}
		})
	}
}

// TestLoginAttemptByIP 测试基于IP的登录限制
func TestLoginAttemptByIP(t *testing.T) {
	// 初始化测试环境
	svcCtx := fixtures.SetupTestDatabase()
	router := fixtures.SetupTestServer(svcCtx)

	// 创建多个测试用户
	users := []string{"user1", "user2", "user3"}
	for _, username := range users {
		testUser := &model.User{
			Username: username,
			Email:    fmt.Sprintf("%<EMAIL>", username),
			Password: "$argon2id$v=19$m=65536,t=3,p=2$c29tZXNhbHQ$hash",
			Nickname: fmt.Sprintf("Test %s", username),
			Status:   1,
		}
		err := svcCtx.UserModel.Create(testUser)
		assert.NoError(t, err)
	}

	// 使用同一IP对不同用户进行登录尝试
	testIP := "*************"
	
	// 前4次尝试应该正常失败
	for i := 0; i < 4; i++ {
		loginReq := types.UserLoginReq{
			Username: users[i%len(users)],
			Password: "wrongpassword",
		}
		reqBody, _ := json.Marshal(loginReq)

		req, _ := http.NewRequest("POST", "/api/public/user/login", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-Real-IP", testIP)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, 200, w.Code)
		
		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		
		if message, ok := response["message"].(string); ok {
			assert.Contains(t, message, "密码错误")
		}
	}

	// 第5次尝试应该被IP限制阻止
	loginReq := types.UserLoginReq{
		Username: "user1",
		Password: "wrongpassword",
	}
	reqBody, _ := json.Marshal(loginReq)

	req, _ := http.NewRequest("POST", "/api/public/user/login", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Real-IP", testIP)

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, 200, w.Code)
	
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	
	if message, ok := response["message"].(string); ok {
		assert.Contains(t, message, "登录失败次数过多，账户已被锁定，请稍后再试")
	}
}

// TestLoginAttemptModel 测试LoginAttemptModel的方法
func TestLoginAttemptModel(t *testing.T) {
	// 初始化测试环境
	svcCtx := fixtures.SetupTestDatabase()

	// 创建测试数据
	attempts := []*model.LoginAttempt{
		{
			Username:  "testuser",
			Ip:        "*************",
			UserAgent: "Test Agent",
			Success:   false,
			FailType:  "password_wrong",
			CreatedAt: time.Now().Add(-10 * time.Minute),
		},
		{
			Username:  "testuser",
			Ip:        "*************",
			UserAgent: "Test Agent",
			Success:   false,
			FailType:  "password_wrong",
			CreatedAt: time.Now().Add(-5 * time.Minute),
		},
		{
			Username:  "testuser",
			Ip:        "*************",
			UserAgent: "Test Agent",
			Success:   true,
			FailType:  "",
			CreatedAt: time.Now(),
		},
	}

	for _, attempt := range attempts {
		err := svcCtx.LoginAttemptModel.Create(attempt)
		assert.NoError(t, err)
	}

	// 测试获取最近失败次数
	failCount, err := svcCtx.LoginAttemptModel.GetRecentFailedAttemptsByUsername("testuser", 15*time.Minute)
	assert.NoError(t, err)
	assert.Equal(t, int64(2), failCount)

	// 测试获取统计信息
	stats, err := svcCtx.LoginAttemptModel.GetStatistics(24 * time.Hour)
	assert.NoError(t, err)
	assert.Equal(t, int64(3), stats.TotalAttempts)
	assert.Equal(t, int64(1), stats.SuccessAttempts)
	assert.Equal(t, int64(2), stats.FailedAttempts)
	assert.True(t, stats.SuccessRate > 0)

	// 测试清理旧记录
	err = svcCtx.LoginAttemptModel.CleanOldAttempts(1 * time.Hour)
	assert.NoError(t, err)
}

// TestAdminLoginAttemptLimit 测试管理员登录失败次数限制
func TestAdminLoginAttemptLimit(t *testing.T) {
	// 初始化测试环境
	svcCtx := fixtures.SetupTestDatabase()
	router := fixtures.SetupTestServer(svcCtx)

	// 创建测试管理员
	testAdmin := &model.Admin{
		Username: "testadmin",
		Email:    "<EMAIL>",
		Password: "$argon2id$v=19$m=65536,t=3,p=2$c29tZXNhbHQ$hash",
		Nickname: "Test Admin",
		Role:     "admin",
		Status:   1,
	}
	err := svcCtx.AdminModel.Create(testAdmin)
	assert.NoError(t, err)

	// 进行5次失败的登录尝试
	for i := 0; i < 5; i++ {
		loginReq := types.AdminLoginReq{
			Username: "testadmin",
			Password: "wrongpassword",
		}
		reqBody, _ := json.Marshal(loginReq)

		req, _ := http.NewRequest("POST", "/api/public/admin/login", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-Real-IP", "*************")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, 401, w.Code) // 管理员登录失败返回401
	}

	// 第6次尝试应该被阻止
	loginReq := types.AdminLoginReq{
		Username: "testadmin",
		Password: "wrongpassword",
	}
	reqBody, _ := json.Marshal(loginReq)

	req, _ := http.NewRequest("POST", "/api/public/admin/login", bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Real-IP", "*************")

	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, 401, w.Code)
	
	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	
	if message, ok := response["message"].(string); ok {
		assert.Contains(t, message, "登录失败次数过多，账户已被锁定，请稍后再试")
	}
}
