-- 数据库迁移脚本：修改状态枚举值
-- 将所有表中的状态字段从 0/1 改为 1/2
-- 执行前请备份数据库！

-- 1. 更新用户表状态
UPDATE users SET status = 2 WHERE status = 0;
-- 现在状态值：1=启用，2=禁用

-- 2. 更新管理员表状态  
UPDATE admins SET status = 2 WHERE status = 0;
-- 现在状态值：1=启用，2=禁用

-- 3. 更新域名表状态
UPDATE domains SET status = 2 WHERE status = 0;
-- 现在状态值：1=启用，2=禁用

-- 4. 更新邮箱表状态
UPDATE mailboxes SET status = 2 WHERE status = 0;
-- 现在状态值：1=启用，2=禁用

-- 5. 更新API密钥表状态
UPDATE api_keys SET status = 2 WHERE status = 0;
-- 现在状态值：1=启用，2=禁用

-- 6. 更新验证规则表状态
UPDATE verification_rules SET status = 2 WHERE status = 0;
-- 现在状态值：1=启用，2=禁用

-- 7. 更新用户状态枚举（如果有其他状态值）
-- 用户状态：1=激活，2=未激活，3=锁定
UPDATE users SET status = 2 WHERE status = 0; -- 未激活
-- 锁定状态保持为3不变

-- 验证迁移结果
SELECT 'users' as table_name, status, COUNT(*) as count FROM users GROUP BY status
UNION ALL
SELECT 'admins' as table_name, status, COUNT(*) as count FROM admins GROUP BY status  
UNION ALL
SELECT 'domains' as table_name, status, COUNT(*) as count FROM domains GROUP BY status
UNION ALL
SELECT 'mailboxes' as table_name, status, COUNT(*) as count FROM mailboxes GROUP BY status
UNION ALL
SELECT 'api_keys' as table_name, status, COUNT(*) as count FROM api_keys GROUP BY status
UNION ALL
SELECT 'verification_rules' as table_name, status, COUNT(*) as count FROM verification_rules GROUP BY status
ORDER BY table_name, status;

-- 注意事项：
-- 1. 执行前请备份数据库
-- 2. 确保应用程序已更新为使用新的枚举值
-- 3. 测试所有功能是否正常工作
-- 4. 如果需要回滚，将所有 status=2 改回 status=0
