@echo off
setlocal enabledelayedexpansion

REM 邮件管理系统自动化测试脚本 (Windows版本)
REM 使用curl命令进行API测试

REM 配置
set BASE_URL=http://localhost:8080
set CONTENT_TYPE=application/json
set TEST_EMAIL=<EMAIL>
set TEST_USERNAME=autotest_user
set TEST_PASSWORD=AutoTest123!

REM 测试结果统计
set TOTAL_TESTS=0
set PASSED_TESTS=0
set FAILED_TESTS=0

REM 日志文件
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set LOG_FILE=test_results_%YYYY%%MM%%DD%_%HH%%Min%%Sec%.log

echo 邮件管理系统API测试开始
echo 测试开始时间: %date% %time% > %LOG_FILE%
echo 基础URL: %BASE_URL% >> %LOG_FILE%
echo. >> %LOG_FILE%

REM 函数：打印测试标题
:print_test_title
echo.
echo === %~1 ===
echo === %~1 === >> %LOG_FILE%
goto :eof

REM 函数：测试API
:test_api
set method=%~1
set url=%~2
set data=%~3
set expected_code=%~4
set test_name=%~5
set auth_header=%~6

set /a TOTAL_TESTS+=1

if "%auth_header%"=="" (
    curl -s -w "%%{http_code}" -X %method% -H "Content-Type: %CONTENT_TYPE%" -d "%data%" "%BASE_URL%%url%" > temp_response.txt 2>nul
) else (
    curl -s -w "%%{http_code}" -X %method% -H "Content-Type: %CONTENT_TYPE%" -H "Authorization: Bearer %auth_header%" -d "%data%" "%BASE_URL%%url%" > temp_response.txt 2>nul
)

REM 读取响应码（最后3个字符）
for /f %%i in (temp_response.txt) do set response=%%i
set http_code=%response:~-3%

echo Request: %method% %url% >> %LOG_FILE%
echo Response Code: %http_code% >> %LOG_FILE%
echo Response Body: %response% >> %LOG_FILE%
echo --- >> %LOG_FILE%

if "%http_code%"=="%expected_code%" (
    echo [PASS] %test_name%
    echo [PASS] %test_name% >> %LOG_FILE%
    set /a PASSED_TESTS+=1
) else (
    echo [FAIL] %test_name% (Expected: %expected_code%, Got: %http_code%)
    echo [FAIL] %test_name% (Expected: %expected_code%, Got: %http_code%) >> %LOG_FILE%
    set /a FAILED_TESTS+=1
)

del temp_response.txt 2>nul
goto :eof

REM 开始测试

REM 1. 健康检查
call :print_test_title "健康检查"
call :test_api "GET" "/api/health" "" "200" "系统健康检查"

REM 2. 用户注册测试
call :print_test_title "用户注册功能"

REM 2.1 发送验证码
call :test_api "POST" "/api/public/send-code" "{\"type\":\"email\",\"target\":\"%TEST_EMAIL%\",\"length\":6,\"expireMinutes\":10}" "200" "发送注册验证码"

REM 2.2 用户注册
call :test_api "POST" "/api/public/user/register" "{\"username\":\"%TEST_USERNAME%\",\"email\":\"%TEST_EMAIL%\",\"password\":\"%TEST_PASSWORD%\",\"nickname\":\"自动测试用户\"}" "200" "用户注册"

REM 3. 用户登录测试
call :print_test_title "用户登录功能"

REM 3.1 正常登录
curl -s -X POST -H "Content-Type: %CONTENT_TYPE%" -d "{\"username\":\"%TEST_USERNAME%\",\"password\":\"%TEST_PASSWORD%\"}" "%BASE_URL%/api/public/user/login" > login_response.txt 2>nul

REM 简单的token提取（需要根据实际响应格式调整）
for /f "tokens=*" %%i in (login_response.txt) do set login_response=%%i
echo Login Response: %login_response% >> %LOG_FILE%

REM 这里需要根据实际的JSON响应格式来提取token
REM 暂时使用假token进行后续测试
set AUTH_TOKEN=dummy_token_for_testing

if not "%login_response%"=="" (
    echo [PASS] 用户登录并获取Token
    echo [PASS] 用户登录并获取Token >> %LOG_FILE%
    set /a PASSED_TESTS+=1
) else (
    echo [FAIL] 用户登录并获取Token
    echo [FAIL] 用户登录并获取Token >> %LOG_FILE%
    set /a FAILED_TESTS+=1
)
set /a TOTAL_TESTS+=1

del login_response.txt 2>nul

REM 3.2 错误密码登录
call :test_api "POST" "/api/public/user/login" "{\"username\":\"%TEST_USERNAME%\",\"password\":\"wrong_password\"}" "200" "错误密码登录（应该失败）"

REM 4. 用户资料管理测试
call :print_test_title "用户资料管理"

REM 4.1 获取用户资料
call :test_api "GET" "/api/user/profile" "" "200" "获取用户资料" "%AUTH_TOKEN%"

REM 4.2 更新用户资料
call :test_api "PUT" "/api/user/profile" "{\"nickname\":\"更新后的自动测试用户\"}" "200" "更新用户资料" "%AUTH_TOKEN%"

REM 5. 邮箱管理测试
call :print_test_title "邮箱管理功能"

REM 5.1 获取邮箱提供商配置
call :test_api "GET" "/api/user/mailboxes/providers" "" "200" "获取邮箱提供商配置" "%AUTH_TOKEN%"

REM 5.2 获取邮箱列表
call :test_api "GET" "/api/user/mailboxes" "" "200" "获取邮箱列表" "%AUTH_TOKEN%"

REM 5.3 获取邮箱统计
call :test_api "GET" "/api/user/mailboxes/stats" "" "200" "获取邮箱统计" "%AUTH_TOKEN%"

REM 6. 邮件管理测试
call :print_test_title "邮件管理功能"

REM 6.1 获取邮件列表
call :test_api "GET" "/api/user/emails" "" "200" "获取邮件列表" "%AUTH_TOKEN%"

REM 6.2 获取已发送邮件
call :test_api "GET" "/api/user/emails?direction=sent" "" "200" "获取已发送邮件" "%AUTH_TOKEN%"

REM 7. 权限测试
call :print_test_title "权限控制测试"

REM 7.1 无token访问受保护资源
call :test_api "GET" "/api/user/profile" "" "401" "无token访问用户资料（应该失败）"

REM 测试结果统计
call :print_test_title "测试结果统计"
echo 总测试数: %TOTAL_TESTS%
echo 通过测试: %PASSED_TESTS%
echo 失败测试: %FAILED_TESTS%

if %FAILED_TESTS%==0 (
    echo 🎉 所有测试通过！
    set exit_code=0
) else (
    echo ❌ 有 %FAILED_TESTS% 个测试失败
    set exit_code=1
)

REM 写入统计信息到日志
echo. >> %LOG_FILE%
echo === 测试结果统计 === >> %LOG_FILE%
echo 总测试数: %TOTAL_TESTS% >> %LOG_FILE%
echo 通过测试: %PASSED_TESTS% >> %LOG_FILE%
echo 失败测试: %FAILED_TESTS% >> %LOG_FILE%
echo 测试结束时间: %date% %time% >> %LOG_FILE%

echo 详细日志已保存到: %LOG_FILE%

pause
exit /b %exit_code%