### 用户资料管理API测试

# 测试环境配置
@baseUrl = http://localhost:8080
@contentType = application/json

# 需要先登录获取token
@authToken = Bearer_TOKEN_HERE

### 1. 获取用户资料
GET {{baseUrl}}/api/user/profile
Authorization: Bearer {{authToken}}

### 2. 更新用户昵称
PUT {{baseUrl}}/api/user/profile
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "nickname": "更新后的昵称"
}

### 3. 更新用户头像
PUT {{baseUrl}}/api/user/profile
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "avatar": "https://example.com/avatar/new-avatar.jpg"
}

### 4. 同时更新昵称和头像
PUT {{baseUrl}}/api/user/profile
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "nickname": "新昵称",
  "avatar": "https://example.com/avatar/updated-avatar.jpg"
}

### 5. 尝试更新邮箱（应该失败，如果不允许）
PUT {{baseUrl}}/api/user/profile
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "email": "<EMAIL>"
}

### 6. 尝试更新用户名（应该失败，如果不允许）
PUT {{baseUrl}}/api/user/profile
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "username": "newusername"
}

### 7. 测试空昵称更新
PUT {{baseUrl}}/api/user/profile
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "nickname": ""
}

### 8. 测试过长昵称更新
PUT {{baseUrl}}/api/user/profile
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "nickname": "这是一个非常非常非常长的昵称，用来测试系统是否会正确处理过长的昵称输入，看看会不会被截断或者报错"
}

### 9. 测试特殊字符昵称
PUT {{baseUrl}}/api/user/profile
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "nickname": "昵称@#$%^&*()_+{}|:<>?[]\\;'\",./"
}

### 10. 测试无效头像URL
PUT {{baseUrl}}/api/user/profile
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "avatar": "not-a-valid-url"
}