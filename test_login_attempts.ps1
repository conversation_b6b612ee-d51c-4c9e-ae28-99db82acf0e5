# 测试登录失败次数限制功能
Write-Host "开始测试登录失败次数限制功能..." -ForegroundColor Green

# 测试用户登录失败
Write-Host "`n=== 测试用户登录失败次数限制 ===" -ForegroundColor Yellow

for ($i = 1; $i -le 6; $i++) {
    Write-Host "第 $i 次登录尝试..." -ForegroundColor Cyan
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8080/api/public/user/login" -Method POST -Headers @{"Content-Type"="application/json"} -Body '{"username":"testuser","password":"wrongpassword"}' -ErrorAction Stop
        
        $content = $response.Content | ConvertFrom-Json
        Write-Host "响应: $($content.msg)" -ForegroundColor White
        
        # 如果返回锁定消息，说明限制生效了
        if ($content.msg -like "*锁定*") {
            Write-Host "✅ 登录失败次数限制功能正常工作！" -ForegroundColor Green
            break
        }
    }
    catch {
        Write-Host "请求失败: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Start-Sleep -Seconds 1
}

# 测试管理员登录失败
Write-Host "`n=== 测试管理员登录失败次数限制 ===" -ForegroundColor Yellow

for ($i = 1; $i -le 6; $i++) {
    Write-Host "管理员第 $i 次登录尝试..." -ForegroundColor Cyan
    
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8080/api/public/admin/login" -Method POST -Headers @{"Content-Type"="application/json"} -Body '{"username":"admin","password":"wrongpassword"}' -ErrorAction Stop
        
        $content = $response.Content | ConvertFrom-Json
        Write-Host "响应: $($content.msg)" -ForegroundColor White
        
        # 如果返回锁定消息，说明限制生效了
        if ($content.msg -like "*锁定*") {
            Write-Host "✅ 管理员登录失败次数限制功能正常工作！" -ForegroundColor Green
            break
        }
    }
    catch {
        Write-Host "请求失败: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Start-Sleep -Seconds 1
}

# 测试正确密码登录（应该被锁定）
Write-Host "`n=== 测试锁定期间使用正确密码登录 ===" -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080/api/public/user/login" -Method POST -Headers @{"Content-Type"="application/json"} -Body '{"username":"testuser","password":"testpassword"}' -ErrorAction Stop
    
    $content = $response.Content | ConvertFrom-Json
    Write-Host "使用正确密码登录响应: $($content.msg)" -ForegroundColor White
    
    if ($content.msg -like "*锁定*") {
        Write-Host "✅ 锁定期间正确密码也被阻止，功能正常！" -ForegroundColor Green
    } else {
        Write-Host "❌ 锁定期间正确密码未被阻止，可能有问题！" -ForegroundColor Red
    }
}
catch {
    Write-Host "请求失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n测试完成！" -ForegroundColor Green
