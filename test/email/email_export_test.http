### 邮件导出功能测试

### 1. 用户登录获取Token
POST http://localhost:8080/api/public/user/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "testpassword"
}

### 2. 导出所有邮件为CSV格式（包含内容）
GET http://localhost:8080/api/user/emails/export?format=csv&includeContent=true
Authorization: Bearer {{token}}

### 3. 导出所有邮件为JSON格式（不包含内容）
GET http://localhost:8080/api/user/emails/export?format=json&includeContent=false
Authorization: Bearer {{token}}

### 4. 导出所有邮件为EML格式
GET http://localhost:8080/api/user/emails/export?format=eml
Authorization: Bearer {{token}}

### 5. 按方向筛选导出（已发送邮件）
GET http://localhost:8080/api/user/emails/export?format=csv&direction=sent
Authorization: Bearer {{token}}

### 6. 按方向筛选导出（已接收邮件）
GET http://localhost:8080/api/user/emails/export?format=json&direction=received
Authorization: Bearer {{token}}

### 7. 按主题筛选导出
GET http://localhost:8080/api/user/emails/export?format=csv&subject=测试
Authorization: Bearer {{token}}

### 8. 按发件人筛选导出
GET http://localhost:8080/api/user/emails/export?format=json&fromEmail=<EMAIL>
Authorization: Bearer {{token}}

### 9. 按收件人筛选导出
GET http://localhost:8080/api/user/emails/export?format=csv&toEmail=<EMAIL>
Authorization: Bearer {{token}}

### 10. 按时间范围筛选导出
GET http://localhost:8080/api/user/emails/export?format=json&createdAtStart=2025-01-01T00:00:00Z&createdAtEnd=2025-12-31T23:59:59Z
Authorization: Bearer {{token}}

### 11. 指定邮箱导出
GET http://localhost:8080/api/user/emails/export?format=csv&mailboxId=1
Authorization: Bearer {{token}}

### 12. 组合条件导出（已发送 + 包含内容 + CSV格式）
GET http://localhost:8080/api/user/emails/export?format=csv&direction=sent&includeContent=true&subject=重要
Authorization: Bearer {{token}}

### 13. 测试无效格式（应该返回错误）
GET http://localhost:8080/api/user/emails/export?format=pdf
Authorization: Bearer {{token}}

### 14. 测试无权限的邮箱ID（应该返回错误）
GET http://localhost:8080/api/user/emails/export?format=csv&mailboxId=999
Authorization: Bearer {{token}}

### 15. 下载导出的文件（需要先执行上面的导出操作获取文件名）
GET http://localhost:8080/api/user/emails/download/emails_export_20250731_120000.csv
Authorization: Bearer {{token}}

### 16. 测试下载不存在的文件（应该返回错误）
GET http://localhost:8080/api/user/emails/download/nonexistent_file.csv
Authorization: Bearer {{token}}

### 测试说明：
# 1. 首先需要登录获取token，然后在后续请求中使用
# 2. 支持的导出格式：csv, json, eml
# 3. 支持的筛选条件：
#    - mailboxId: 指定邮箱ID
#    - subject: 主题关键词（模糊搜索）
#    - fromEmail: 发件人邮箱
#    - toEmail: 收件人邮箱
#    - direction: 邮件方向（sent/received）
#    - createdAtStart: 创建时间开始
#    - createdAtEnd: 创建时间结束
#    - includeContent: 是否包含邮件内容（true/false）
# 4. 导出成功后会返回文件信息和下载链接
# 5. 使用下载链接可以下载导出的文件

### 预期响应格式：
# 导出成功响应：
# {
#   "code": 10000,
#   "msg": "success",
#   "data": {
#     "fileName": "emails_export_20250731_120000.csv",
#     "fileSize": 1024,
#     "recordCount": 10,
#     "downloadUrl": "/api/user/emails/download/emails_export_20250731_120000.csv"
#   }
# }

### CSV格式示例：
# ID,邮箱ID,主题,发件人,收件人,抄送,密送,方向,是否已读,是否星标,发送时间,接收时间,创建时间,内容类型,邮件内容
# 1,1,"测试邮件","<EMAIL>","<EMAIL>","","","received","false","false","","2025-01-01 12:00:00","2025-01-01 12:00:00","text","邮件内容"

### JSON格式示例：
# [
#   {
#     "id": 1,
#     "mailbox_id": 1,
#     "subject": "测试邮件",
#     "from_email": "<EMAIL>",
#     "to_emails": "<EMAIL>",
#     "direction": "received",
#     "is_read": false,
#     "is_starred": false,
#     "created_at": "2025-01-01T12:00:00Z"
#   }
# ]

### EML格式示例：
# Message-ID: <unique-id>
# Subject: 测试邮件
# <AUTHOR> <EMAIL>
# To: <EMAIL>
# Date: Mon, 01 Jan 2025 12:00:00 +0800
# Content-Type: text/plain; charset=utf-8
# 
# 邮件内容...
