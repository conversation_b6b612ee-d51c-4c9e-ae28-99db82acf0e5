### 邮件接收和列表API测试

# 测试环境配置
@baseUrl = http://localhost:8080
@contentType = application/json

# 需要先登录获取token
@authToken = Bearer_TOKEN_HERE

### 1. 获取收件箱邮件列表
GET {{baseUrl}}/api/user/emails?direction=received
Authorization: Bearer {{authToken}}

### 2. 获取已发送邮件列表
GET {{baseUrl}}/api/user/emails?direction=sent
Authorization: Bearer {{authToken}}

### 3. 分页获取邮件列表
GET {{baseUrl}}/api/user/emails?page=1&pageSize=10
Authorization: Bearer {{authToken}}

### 4. 按主题搜索邮件
GET {{baseUrl}}/api/user/emails?subject=测试
Authorization: Bearer {{authToken}}

### 5. 按发件人搜索邮件
GET {{baseUrl}}/api/user/emails?fromEmail=<EMAIL>
Authorization: Bearer {{authToken}}

### 6. 按收件人搜索邮件
GET {{baseUrl}}/api/user/emails?toEmails=<EMAIL>
Authorization: Bearer {{authToken}}

### 7. 按时间范围查询邮件
GET {{baseUrl}}/api/user/emails?createdAtStart=2024-01-01T00:00:00Z&createdAtEnd=2024-12-31T23:59:59Z
Authorization: Bearer {{authToken}}

### 8. 按邮箱ID筛选邮件
GET {{baseUrl}}/api/user/emails?mailboxId=1
Authorization: Bearer {{authToken}}

### 9. 组合查询条件
GET {{baseUrl}}/api/user/emails?direction=received&page=1&pageSize=5&subject=测试
Authorization: Bearer {{authToken}}

### 10. 获取特定邮件详情
# 注意：需要替换为实际的邮件ID
GET {{baseUrl}}/api/user/emails/1
Authorization: Bearer {{authToken}}

### 11. 标记邮件为已读
PUT {{baseUrl}}/api/user/emails/1/read
Authorization: Bearer {{authToken}}

### 12. 标记邮件为星标
PUT {{baseUrl}}/api/user/emails/1/star
Authorization: Bearer {{authToken}}

### 13. 取消邮件星标
PUT {{baseUrl}}/api/user/emails/1/star
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "isStarred": false
}

### 14. 删除邮件
DELETE {{baseUrl}}/api/user/emails/1
Authorization: Bearer {{authToken}}

### 15. 批量操作邮件
POST {{baseUrl}}/api/user/emails/batch
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "emailIds": ["1", "2", "3"],
  "action": "markRead",
  "data": {
    "isRead": true
  }
}

### 16. 批量标记星标
POST {{baseUrl}}/api/user/emails/batch
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "emailIds": ["1", "2", "3"],
  "action": "markStar",
  "data": {
    "isStarred": true
  }
}

### 17. 批量删除邮件
POST {{baseUrl}}/api/user/emails/batch
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "emailIds": ["1", "2", "3"],
  "action": "delete"
}

### 18. 尝试访问不存在的邮件（应该失败）
GET {{baseUrl}}/api/user/emails/999
Authorization: Bearer {{authToken}}

### 19. 尝试操作不存在的邮件（应该失败）
PUT {{baseUrl}}/api/user/emails/999/read
Authorization: Bearer {{authToken}}

### 20. 无token访问邮件列表（应该失败）
GET {{baseUrl}}/api/user/emails

### 21. 测试邮件搜索功能
GET {{baseUrl}}/api/user/emails/search?query=重要&filters={"isStarred":true}
Authorization: Bearer {{authToken}}

### 22. 获取邮件统计信息
GET {{baseUrl}}/api/user/emails/stats
Authorization: Bearer {{authToken}}

### 23. 测试邮件导出功能
GET {{baseUrl}}/api/user/emails/export?format=csv&startDate=2024-01-01&endDate=2024-12-31
Authorization: Bearer {{authToken}}

### 24. 测试邮件同步
POST {{baseUrl}}/api/user/emails/sync
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "mailboxId": 1
}

### 25. 测试获取邮件附件
GET {{baseUrl}}/api/user/emails/1/attachments
Authorization: Bearer {{authToken}}