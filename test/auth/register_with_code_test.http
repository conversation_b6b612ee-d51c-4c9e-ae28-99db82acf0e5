### 带验证码的用户注册测试

# 测试环境配置
@baseUrl = http://localhost:8080
@contentType = application/json

### 1. 发送注册邮箱验证码
POST {{baseUrl}}/api/public/send-code
Content-Type: {{contentType}}

{
  "type": "email",
  "target": "<EMAIL>",
  "length": 6,
  "expireMinutes": 10
}

### 2. 使用验证码注册用户（需要先执行步骤1获取验证码）
POST {{baseUrl}}/api/public/user/register
Content-Type: {{contentType}}

{
  "username": "newuser001",
  "email": "<EMAIL>",
  "password": "password123",
  "nickname": "新用户001",
  "code": "123456"
}

### 3. 使用错误验证码注册（应该失败）
POST {{baseUrl}}/api/public/user/register
Content-Type: {{contentType}}

{
  "username": "newuser002",
  "email": "<EMAIL>",
  "password": "password123",
  "nickname": "新用户002",
  "code": "000000"
}

### 4. 使用过期验证码注册（应该失败）
# 注意：需要等待验证码过期后测试
POST {{baseUrl}}/api/public/user/register
Content-Type: {{contentType}}

{
  "username": "newuser003",
  "email": "<EMAIL>",
  "password": "password123",
  "nickname": "新用户003",
  "code": "123456"
}

### 5. 不使用验证码注册（应该成功，如果系统允许）
POST {{baseUrl}}/api/public/user/register
Content-Type: {{contentType}}

{
  "username": "newuser004",
  "email": "<EMAIL>",
  "password": "password123",
  "nickname": "新用户004"
}

### 6. 发送验证码到已存在的邮箱
POST {{baseUrl}}/api/public/send-code
Content-Type: {{contentType}}

{
  "type": "email",
  "target": "<EMAIL>",
  "length": 6,
  "expireMinutes": 10
}

### 7. 测试验证码长度变化
POST {{baseUrl}}/api/public/send-code
Content-Type: {{contentType}}

{
  "type": "email",
  "target": "<EMAIL>",
  "length": 4,
  "expireMinutes": 5
}

### 8. 测试短过期时间验证码
POST {{baseUrl}}/api/public/send-code
Content-Type: {{contentType}}

{
  "type": "email",
  "target": "<EMAIL>",
  "length": 6,
  "expireMinutes": 1
}