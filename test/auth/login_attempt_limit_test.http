### 登录失败次数限制测试

### 1. 第一次登录失败 - 应该正常返回密码错误
POST http://localhost:8080/api/public/user/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "wrongpassword"
}

### 2. 第二次登录失败 - 应该正常返回密码错误
POST http://localhost:8080/api/public/user/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "wrongpassword"
}

### 3. 第三次登录失败 - 应该正常返回密码错误
POST http://localhost:8080/api/public/user/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "wrongpassword"
}

### 4. 第四次登录失败 - 应该正常返回密码错误
POST http://localhost:8080/api/public/user/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "wrongpassword"
}

### 5. 第五次登录失败 - 应该正常返回密码错误
POST http://localhost:8080/api/public/user/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "wrongpassword"
}

### 6. 第六次登录失败 - 应该返回账户被锁定
POST http://localhost:8080/api/public/user/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "wrongpassword"
}

### 7. 使用正确密码登录 - 应该也被锁定
POST http://localhost:8080/api/public/user/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "correctpassword"
}

### 8. 测试不同用户名但相同IP - 应该被IP限制
POST http://localhost:8080/api/public/user/login
Content-Type: application/json

{
  "username": "anotheruser",
  "password": "wrongpassword"
}

### 管理员登录失败次数限制测试

### 9. 管理员第一次登录失败
POST http://localhost:8080/api/public/admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "wrongpassword"
}

### 10. 管理员第二次登录失败
POST http://localhost:8080/api/public/admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "wrongpassword"
}

### 11. 管理员第三次登录失败
POST http://localhost:8080/api/public/admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "wrongpassword"
}

### 12. 管理员第四次登录失败
POST http://localhost:8080/api/public/admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "wrongpassword"
}

### 13. 管理员第五次登录失败
POST http://localhost:8080/api/public/admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "wrongpassword"
}

### 14. 管理员第六次登录失败 - 应该被锁定
POST http://localhost:8080/api/public/admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "wrongpassword"
}

### 测试用例说明：
# 1. 默认配置中最大登录尝试次数为5次，锁定时长为300秒（5分钟）
# 2. 系统会同时检查用户名和IP的失败次数
# 3. 任一条件超过限制都会触发锁定
# 4. 锁定期间即使使用正确密码也无法登录
# 5. 管理员和普通用户使用相同的限制规则

### 等待锁定时间过期后的测试（5分钟后）

### 15. 锁定时间过期后正常登录 - 应该成功（需要等待5分钟）
POST http://localhost:8080/api/public/user/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "correctpassword"
}

### 16. 锁定时间过期后管理员正常登录 - 应该成功（需要等待5分钟）
POST http://localhost:8080/api/public/admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}
