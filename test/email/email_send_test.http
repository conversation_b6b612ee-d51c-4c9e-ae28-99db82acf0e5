### 邮件发送API测试

# 测试环境配置
@baseUrl = http://localhost:8080
@contentType = application/json

# 需要先登录获取token
@authToken = Bearer_TOKEN_HERE

### 1. 发送基础文本邮件
POST {{baseUrl}}/api/user/emails/send
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "mailboxId": 1,
  "subject": "测试邮件主题",
  "fromEmail": "<EMAIL>",
  "toEmail": "<EMAIL>",
  "content": "这是一封测试邮件的内容。",
  "contentType": "text"
}

### 2. 发送HTML格式邮件
POST {{baseUrl}}/api/user/emails/send
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "mailboxId": 1,
  "subject": "HTML格式测试邮件",
  "fromEmail": "<EMAIL>",
  "toEmail": "<EMAIL>",
  "content": "<h1>HTML邮件标题</h1><p>这是一封<strong>HTML格式</strong>的测试邮件。</p><ul><li>列表项1</li><li>列表项2</li></ul>",
  "contentType": "html"
}

### 3. 发送带抄送的邮件
POST {{baseUrl}}/api/user/emails/send
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "mailboxId": 1,
  "subject": "带抄送的测试邮件",
  "fromEmail": "<EMAIL>",
  "toEmail": "<EMAIL>",
  "ccEmail": "<EMAIL>,<EMAIL>",
  "content": "这是一封带抄送的测试邮件。",
  "contentType": "text"
}

### 4. 发送带密送的邮件
POST {{baseUrl}}/api/user/emails/send
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "mailboxId": 1,
  "subject": "带密送的测试邮件",
  "fromEmail": "<EMAIL>",
  "toEmail": "<EMAIL>",
  "bccEmail": "<EMAIL>,<EMAIL>",
  "content": "这是一封带密送的测试邮件。",
  "contentType": "text"
}

### 5. 发送给多个收件人
POST {{baseUrl}}/api/user/emails/send
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "mailboxId": 1,
  "subject": "多收件人测试邮件",
  "fromEmail": "<EMAIL>",
  "toEmail": "<EMAIL>,<EMAIL>,<EMAIL>",
  "content": "这是一封发送给多个收件人的测试邮件。",
  "contentType": "text"
}

### 6. 发送完整的邮件（包含所有字段）
POST {{baseUrl}}/api/user/emails/send
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "mailboxId": 1,
  "subject": "完整测试邮件",
  "fromEmail": "<EMAIL>",
  "toEmail": "<EMAIL>",
  "ccEmail": "<EMAIL>",
  "bccEmail": "<EMAIL>",
  "content": "<h2>完整测试邮件</h2><p>这是一封包含所有字段的测试邮件：</p><ul><li>收件人</li><li>抄送</li><li>密送</li><li>HTML内容</li></ul>",
  "contentType": "html"
}

### 7. 测试空主题邮件（应该失败）
POST {{baseUrl}}/api/user/emails/send
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "mailboxId": 1,
  "subject": "",
  "fromEmail": "<EMAIL>",
  "toEmail": "<EMAIL>",
  "content": "这是一封没有主题的邮件。",
  "contentType": "text"
}

### 8. 测试空收件人邮件（应该失败）
POST {{baseUrl}}/api/user/emails/send
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "mailboxId": 1,
  "subject": "测试邮件",
  "fromEmail": "<EMAIL>",
  "toEmail": "",
  "content": "这是一封没有收件人的邮件。",
  "contentType": "text"
}

### 9. 测试空内容邮件（应该失败）
POST {{baseUrl}}/api/user/emails/send
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "mailboxId": 1,
  "subject": "空内容测试",
  "fromEmail": "<EMAIL>",
  "toEmail": "<EMAIL>",
  "content": "",
  "contentType": "text"
}

### 10. 测试不存在的邮箱ID（应该失败）
POST {{baseUrl}}/api/user/emails/send
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "mailboxId": 999,
  "subject": "测试邮件",
  "fromEmail": "<EMAIL>",
  "toEmail": "<EMAIL>",
  "content": "使用不存在的邮箱ID发送邮件。",
  "contentType": "text"
}

### 11. 测试无效邮箱格式（应该失败）
POST {{baseUrl}}/api/user/emails/send
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "mailboxId": 1,
  "subject": "测试邮件",
  "fromEmail": "<EMAIL>",
  "toEmail": "invalid-email-format",
  "content": "发送到无效邮箱格式。",
  "contentType": "text"
}

### 12. 测试超长主题
POST {{baseUrl}}/api/user/emails/send
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "mailboxId": 1,
  "subject": "这是一个非常非常非常长的邮件主题，用来测试系统是否能正确处理超长的邮件主题，看看会不会被截断或者报错，这个主题应该超过了正常的长度限制",
  "fromEmail": "<EMAIL>",
  "toEmail": "<EMAIL>",
  "content": "测试超长主题的邮件内容。",
  "contentType": "text"
}

### 13. 无token发送邮件（应该失败）
POST {{baseUrl}}/api/user/emails/send
Content-Type: {{contentType}}

{
  "mailboxId": 1,
  "subject": "无权限测试",
  "fromEmail": "<EMAIL>",
  "toEmail": "<EMAIL>",
  "content": "无token发送邮件测试。",
  "contentType": "text"
}