### 集成测试 - 完整用户流程

# 测试环境配置
@baseUrl = http://localhost:8080
@contentType = application/json

### 第一步：用户注册流程

### 1.1 发送注册验证码
POST {{baseUrl}}/api/public/send-code
Content-Type: {{contentType}}

{
  "type": "email",
  "target": "<EMAIL>",
  "length": 6,
  "expireMinutes": 10
}

### 1.2 用户注册（需要手动输入验证码）
POST {{baseUrl}}/api/public/user/register
Content-Type: {{contentType}}

{
  "username": "integration_test_user",
  "email": "<EMAIL>",
  "password": "TestPassword123!",
  "nickname": "集成测试用户",
  "code": "VERIFICATION_CODE_HERE"
}

### 第二步：用户登录

### 2.1 用户登录
POST {{baseUrl}}/api/public/user/login
Content-Type: {{contentType}}

{
  "username": "integration_test_user",
  "password": "TestPassword123!"
}

# 保存登录返回的token
@authToken = TOKEN_FROM_LOGIN_RESPONSE

### 第三步：用户资料管理

### 3.1 获取用户资料
GET {{baseUrl}}/api/user/profile
Authorization: Bearer {{authToken}}

### 3.2 更新用户资料
PUT {{baseUrl}}/api/user/profile
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "nickname": "更新后的集成测试用户",
  "avatar": "https://example.com/avatar/test.jpg"
}

### 第四步：邮箱管理

### 4.1 获取邮箱提供商配置
GET {{baseUrl}}/api/user/mailboxes/providers
Authorization: Bearer {{authToken}}

### 4.2 添加Gmail邮箱
POST {{baseUrl}}/api/user/mailboxes
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "app_password_here",
  "type": "third",
  "provider": "gmail",
  "autoReceive": true
}

### 4.3 测试邮箱连接
POST {{baseUrl}}/api/user/mailboxes/test-connection
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "app_password_here",
  "imapHost": "imap.gmail.com",
  "imapPort": 993,
  "imapSsl": true,
  "smtpHost": "smtp.gmail.com",
  "smtpPort": 587,
  "smtpSsl": true
}

### 4.4 获取邮箱列表
GET {{baseUrl}}/api/user/mailboxes
Authorization: Bearer {{authToken}}

### 4.5 获取邮箱统计
GET {{baseUrl}}/api/user/mailboxes/stats
Authorization: Bearer {{authToken}}

### 第五步：邮件发送

### 5.1 发送测试邮件
POST {{baseUrl}}/api/user/emails/send
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "mailboxId": 1,
  "subject": "集成测试邮件",
  "fromEmail": "<EMAIL>",
  "toEmail": "<EMAIL>",
  "content": "<h1>集成测试邮件</h1><p>这是一封通过集成测试发送的邮件。</p>",
  "contentType": "html"
}

### 第六步：邮件管理

### 6.1 获取已发送邮件列表
GET {{baseUrl}}/api/user/emails?direction=sent
Authorization: Bearer {{authToken}}

### 6.2 获取收件箱邮件列表
GET {{baseUrl}}/api/user/emails?direction=received
Authorization: Bearer {{authToken}}

### 6.3 搜索邮件
GET {{baseUrl}}/api/user/emails?subject=集成测试
Authorization: Bearer {{authToken}}

### 第七步：邮箱同步

### 7.1 同步邮箱
POST {{baseUrl}}/api/user/mailboxes/1/sync
Authorization: Bearer {{authToken}}

### 第八步：密码修改

### 8.1 修改密码
POST {{baseUrl}}/api/user/change-password
Authorization: Bearer {{authToken}}
Content-Type: {{contentType}}

{
  "oldPassword": "TestPassword123!",
  "newPassword": "NewTestPassword123!"
}

### 8.2 使用新密码重新登录
POST {{baseUrl}}/api/public/user/login
Content-Type: {{contentType}}

{
  "username": "integration_test_user",
  "password": "NewTestPassword123!"
}

### 第九步：清理测试数据

### 9.1 删除邮箱
DELETE {{baseUrl}}/api/user/mailboxes/1
Authorization: Bearer {{authToken}}

### 9.2 验证邮箱已删除
GET {{baseUrl}}/api/user/mailboxes
Authorization: Bearer {{authToken}}

### 测试完成
# 集成测试流程完成，验证了以下功能：
# 1. 用户注册（包含邮箱验证码）
# 2. 用户登录
# 3. 用户资料管理
# 4. 邮箱添加和管理
# 5. 邮件发送
# 6. 邮件列表和搜索
# 7. 邮箱同步
# 8. 密码修改
# 9. 数据清理